import * as THREE from 'three';
import AvatarObject from "./Avatar/AvatarObject";
import {IInitDefaultOptions} from "../constant/type";
import AvatarData from "./Avatar/Data/AvatarData";
import AvatarRenderer from "../renderAvatar/AvatarRenderer";
import {GLTFLoader} from "three/examples/jsm/loaders/GLTFLoader";
import {getOrdLink} from "../utils";
import {AVATAR_INSCRIPTION_ID} from "../constant";
import {AvatarUtil} from "./AvatarUtil";

const lightDebugOn = false;

export default class AvatarMain {

    private avatarObject: AvatarObject | undefined;
    private renderer: AvatarRenderer | undefined;
    private delayAvatarData: AvatarData | null = null;

    private onLoad = (avatarObject: AvatarObject) => {

    }
    private loadingObject = false

    domElement: HTMLElement | null = null

    init(options: IInitDefaultOptions, domElement: HTMLElement | null) {
        this.domElement = domElement
        if (domElement) {
            this.renderer = new AvatarRenderer(domElement);
            this.renderer.init(options.transparentBackground)
        }
        this.onLoad = (avatarObject) => {
            options.onLoad(avatarObject)
            this.renderer?.setAvatarObject(avatarObject);
        }
    }


    // -----------------------------------------------------------------------------------------------------------------
    // Public API

    setAvatarData(avatarData: AvatarData | null) {
        if (this.loadingObject) {
            this.delayAvatarData = avatarData
            return
        }

        if (this.avatarObject == null) {
            this.delayAvatarData = avatarData;
            this.createAvatarObject()
            return;
        }

        this.avatarObject.setAvatarData(avatarData);
        this.avatarObject.load();
        // this.avatarObject.getAvatarGLB((result) => {
        //     console.log('save result', result)
        //     const loader = new GLTFLoader();
        //     loader.parse(result, '', (gltf) => {
        //         console.log('loader.parse result', gltf)
        //     })
        //
        //     // this.saveString(result, 'avatar.glb')
        // })
    }

    createAvatarObject() {
        this.loadingObject = true
        const loader = new GLTFLoader();
        loader.load(getOrdLink(AVATAR_INSCRIPTION_ID.Body).content, (gltf) => {
                AvatarUtil.init();
                this.loadingObject = false
                this.avatarObject = new AvatarObject(gltf, this.onLoad);
                this.setAvatarData(this.delayAvatarData);
            },
            (xhr: any) => {
            },
            (error: any) => {
                console.log(error);
            }
        );
    }


    saveArrayBuffer(buffer: ArrayBuffer, filename: string) {
        const blob = new Blob([buffer], {type: 'application/octet-stream'});
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
    }

    saveString(text: string, filename: string) {
        const blob = new Blob([text], {type: 'text/plain'});
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
    }

    destroy() {
        if (this.renderer) {
            this.renderer.destroy();
        }
    }
}
