import AvatarPartSkinMesh from "./Part/AvatarPartSkinMesh";

import AvatarAction from "./Part/AvatarAction";
import {getOrdLink} from "../../utils";

import AvatarTexture from "./Part/AvatarTexture";
import {FATHER_TYPE_ENUM, IChildrenInscription, IFatherInscription} from "../../constant/type";
import AvatarPet from "./Part/AvatarPet";

export default class AvatarPartsModel {
    private static didInit: boolean;
    static shirt: any;
    static texture: any;
    static pants: any;
    static hat: any;
    static shoes: any;
    static action: any;
    static pet: any;

    static init(globalParts: IFatherInscription[]) {
        if (!this.didInit) {
            this.shirt = {};

            this.pants = {};

            this.action = {};

            this.hat = {};

            this.shoes = {};

            this.texture = {};

            this.pet = {};
        }
        // register global parts
        for (let i = 0; i < globalParts.length; i++) {
            // TODO type暂时固定，后续会改成typescript
            if (globalParts[i].type === FATHER_TYPE_ENUM.Action) {
                this.registerAction(globalParts[i].childrenInscription);
            } else if (globalParts[i].type === FATHER_TYPE_ENUM.Pet) {
                this.registerPet(globalParts[i].childrenInscription);
            } else if (globalParts[i].type === FATHER_TYPE_ENUM.Pants) {
                this.registerPants(globalParts[i].childrenInscription);
            } else if (globalParts[i].type === FATHER_TYPE_ENUM.Shirt) {
                this.registerShirt(globalParts[i].childrenInscription);
            } else if (globalParts[i].type === FATHER_TYPE_ENUM.Shoes) {
                this.registerShoes(globalParts[i].childrenInscription);
            } else if (globalParts[i].type === FATHER_TYPE_ENUM.Hat) {
                this.registerHat(globalParts[i].childrenInscription);
            }
        }

        this.didInit = true;
    }

    static tryGetPart(collectionName: string, preferredPart: string | null = null, allowRandomize = false) {
        // @ts-ignore
        if (typeof this[collectionName] !== "object") {
            throw new Error(`Invalid collection name: ${collectionName}`);
        }

        if (preferredPart) {
            if (preferredPart === "None") {
                return null;
            }

            // @ts-ignore
            if (typeof this[collectionName][preferredPart] !== "undefined") {
                // @ts-ignore
                return this[collectionName][preferredPart];
            }

            if (collectionName === "action") {
                this[collectionName][preferredPart] = new AvatarAction(preferredPart || "None", preferredPart || "None");
                return this[collectionName][preferredPart];
            } else if (collectionName === "pet") {
                // @ts-ignore
                this[collectionName][preferredPart] = new AvatarPet(preferredPart || "None", preferredPart || "None")
                // @ts-ignore
                return this[collectionName][preferredPart];
            } else {
                // @ts-ignore
                this[collectionName][preferredPart] = new AvatarPartSkinMesh(preferredPart || "None", preferredPart || "None")
                // @ts-ignore
                return this[collectionName][preferredPart];
            }
        }
        if (allowRandomize) {
            return this.getRandomPart(collectionName);
        }
        return null;
    }

    static tryGetTexture(texturePath: string | null = null) {
        // @ts-ignore
        if (typeof this.texture[texturePath] === "undefined") {
            // @ts-ignore
            this.texture[texturePath] = new AvatarTexture(texturePath, texturePath);
        }
        // @ts-ignore
        return this.texture[texturePath]
    }


    static getRandomPart(collectionName: string) {
        // @ts-ignore
        if (typeof this[collectionName] !== "object") {
            throw new Error(`Invalid collection name: ${collectionName}`);
        }
        // @ts-ignore
        const collection = this[collectionName];
        const keys = Object.keys(collection).filter(key => key !== "QuestionMark");
        return collection[keys[keys.length * Math.random() << 0]];
    }

    static registerShirt(childrenInscription: IChildrenInscription[]) {
        for (let i = 0; i < childrenInscription.length; i++) {
            const key = childrenInscription[i].inscriptionId
            if (!childrenInscription[i]) {
                this.shirt[key] = null;
                continue
            }
            this.shirt[key] = new AvatarPartSkinMesh(key, key);
        }
    }

    static registerShoes(childrenInscription: IChildrenInscription[]) {
        for (let i = 0; i < childrenInscription.length; i++) {
            const key = childrenInscription[i].inscriptionId
            if (!childrenInscription[i]) {
                this.shoes[key] = null;
                continue
            }
            this.shoes[key] = new AvatarPartSkinMesh(key, key);
        }
    }

    static registerHat(childrenInscription: IChildrenInscription[]) {
        for (let i = 0; i < childrenInscription.length; i++) {
            const key = childrenInscription[i].inscriptionId
            if (!childrenInscription[i]) {
                this.hat[key] = null;
                continue
            }
            this.hat[key] = new AvatarPartSkinMesh(key, key);
        }
    }


    static registerPants(childrenInscription: IChildrenInscription[]) {
        for (let i = 0; i < childrenInscription.length; i++) {
            const key = childrenInscription[i].inscriptionId
            if (!childrenInscription[i]) {
                this.pants[key] = null;
                continue
            }
            this.pants[key] = new AvatarPartSkinMesh(key, key);
        }
    }

    static registerPet(childrenInscription: IChildrenInscription[]) {
        for (let i = 0; i < childrenInscription.length; i++) {
            const key = childrenInscription[i].inscriptionId
            if (!childrenInscription[i]) {
                this.pet[key] = null;
                continue
            }
            this.pet[key] = new AvatarPet(key, key);
        }
    }

    static registerAction(childrenInscription: IChildrenInscription[]) {
        for (let i = 0; i < childrenInscription.length; i++) {
            const key = childrenInscription[i].inscriptionId
            if (!childrenInscription[i]) {
                this.action[key] = null;
                continue
            }
            //由于首次进入时 会通过 tryGetPart 创建过一次,所以不重复创建
            if (this.action[key]) {
                continue
            }
            this.action[key] = new AvatarAction(key, key);
        }
    }

    static getActionList() {
        const list: AvatarAction[] = [];
        for (const key in this.action) {
            if (this.action.hasOwnProperty(key)) {
                list.push(this.action[key]);
            }
        }
        return list
    }
}
