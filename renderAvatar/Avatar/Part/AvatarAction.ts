import {GLTFLoader} from 'three/examples/jsm/loaders/GLTFLoader';
import {getOrdLink} from "../../../utils";

export default class AvatarAction {
    private id: string;
    private meshPath: string;
    private loadedClip: any;

    constructor(id: string, meshPath: string) {
        this.id = id;
        if (id === meshPath) {
            this.meshPath = getOrdLink(meshPath).content;
        } else {
            this.meshPath = meshPath;
        }
        this.loadedClip = null;
    }

    load(callback: Function) {
        try {
            if (this.loadedClip) {
                callback(this.loadedClip);
                return;
            }
            const loader = new GLTFLoader();
            loader.load(this.meshPath, (gltf) => {
                    // console.debug('[AvatarAction]', 'Loaded glb:', this.meshPath, gltf);

                    const clips = gltf.animations;
                    this.loadedClip = clips[0];
                    callback(this.loadedClip);
                }
            );
        } catch (e) {
            console.error('[AvatarAction]', 'Load error:', e);
            callback(null);
        }
    }
}
