// @ts-ignore
import * as THREE from 'three';
// @ts-ignore
import {GLTFLoader} from 'three/examples/jsm/loaders/GLTFLoader';
import {getOrdLink} from "../../../utils";

export default class AvatarPartSkinMesh {
  private id: string;
  private meshPath: string;
  private loadedObjs: THREE.SkinnedMesh[] = [];

  constructor(id: string, meshPath: string) {
    this.id = id;
    this.meshPath = getOrdLink(meshPath).content;
  }

  load(callback: Function) {
    try {
      if (this.loadedObjs.length > 0) {
        const firstMesh = this.loadedObjs[0];
        const mesh = firstMesh.clone();
        mesh.material = (mesh.material as THREE.MeshPhysicalMaterial).clone();
        const list = [mesh]
        for (let i = 1; i < this.loadedObjs.length; i++) {
          list.push(this.loadedObjs[i])
        }
        callback(list);
        return;
      }
      const loader = new GLTFLoader();
      loader.load(this.meshPath, (gltf: any) => {
          // console.debug('[AvatarPartSkinMesh]', 'Loaded glb:', this.meshPath, gltf);

          const avatar = gltf.scene;
          let meshList: THREE.SkinnedMesh[] = [];
          avatar.traverse(function (child: any) {
            if (child.isMesh) {
              meshList.push(child)
              child.castShadow = true;
              child.receiveShadow = true;
            }
          });
          this.loadedObjs = meshList;
          this.load(callback)
        }
      );
    } catch (e) {
      console.error('[AvatarPartSkinMesh]', 'Load error:', e);
      callback(null);
    }
  }
}
