#!/bin/bash

# JSON 文件上传工具 - Linux/macOS 版本

echo "===================================================="
echo "📦 JSON 文件上传工具"
echo "===================================================="
echo

# 检查 Python 环境
echo "🔍 检查 Python 环境..."
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
    PIP_CMD="pip"
    echo "✅ 找到 Python3: $(python3 --version)"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
    PIP_CMD="pip"
    echo "✅ 找到 Python: $(python --version)"
else
    echo "❌ 未找到 Python，请先安装 Python 3.6+"
    exit 1
fi

echo

# 检查或创建虚拟环境
VENV_DIR="obs_upload_env"
echo "📦 检查虚拟环境..."
if [ ! -d "$VENV_DIR" ]; then
    echo "🔧 创建虚拟环境..."
    $PYTHON_CMD -m venv $VENV_DIR
    if [ $? -ne 0 ]; then
        echo "❌ 创建虚拟环境失败"
        exit 1
    fi
    echo "✅ 虚拟环境创建成功"
else
    echo "✅ 虚拟环境已存在"
fi

# 激活虚拟环境
echo "🔌 激活虚拟环境..."
source $VENV_DIR/bin/activate
if [ $? -ne 0 ]; then
    echo "❌ 激活虚拟环境失败"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖..."
if python -c "import obs" &> /dev/null; then
    echo "✅ OBS SDK 已安装"
else
    echo "⚠️  未找到 OBS SDK，正在安装..."
    pip install esdk-obs-python
    if [ $? -eq 0 ]; then
        echo "✅ 依赖安装成功"
    else
        echo "❌ 依赖安装失败"
        echo "💡 提示：如果遇到权限问题，请尝试使用虚拟环境"
        exit 1
    fi
fi

echo

# 选择运行模式
echo "🎯 请选择运行模式:"
echo "1) 快速测试 (扫描文件)"
echo "2) 演示模式 (模拟上传)"
echo "3) 代理模式 (通过 Vite 代理上传) ← 推荐"
echo "4) 直连模式 (直接连接华为云)"
echo "5) OBS 连接测试"
echo "请输入选择 (1-5): "
read -r mode_choice

case $mode_choice in
    1)
        echo "🚀 运行快速测试..."
        python quick_upload.py
        ;;
    2)
        echo "🎭 运行演示模式..."
        python upload_json_files_demo.py
        ;;
    3)
        echo "🔗 运行代理模式..."
        echo "📝 检查 Vite 开发服务器是否运行..."
        if curl -s -o /dev/null -w "%{http_code}" http://**************:3000 | grep -q "200"; then
            echo "✅ Vite 服务器正在运行"
            python upload_json_files_proxy.py
        else
            echo "❌ Vite 服务器未运行"
            echo "💡 请先启动开发服务器: npm run dev"
        fi
        ;;
    4)
        echo "🚀 运行直连模式..."
        echo "⚠️  注意: 这将直接连接到华为云 OBS"
        echo "确认继续？(y/N)"
        read -r confirm
        if [[ "$confirm" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            python upload_json_files.py
        else
            echo "❌ 用户取消上传"
        fi
        ;;
    5)
        echo "🔧 运行 OBS 连接测试..."
        python test_obs_connection.py
        ;;
    *)
        echo "❌ 无效选择，退出"
        deactivate
        exit 1
        ;;
esac

echo

# 显示报告
echo "📊 查看上传报告..."
if [ -f "upload_report.txt" ]; then
    cat upload_report.txt
else
    echo "⚠️  未找到上传报告文件"
fi

echo
echo "✅ 脚本执行完成"

# 退出虚拟环境
deactivate
