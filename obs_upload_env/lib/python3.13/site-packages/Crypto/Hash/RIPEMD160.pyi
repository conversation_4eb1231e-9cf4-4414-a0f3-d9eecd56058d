from typing import Union

Buffer = Union[bytes, bytearray, memoryview]

class RIPEMD160Hash(object):
    digest_size: int
    block_size: int
    oid: str

    def __init__(self, data: Buffer = ...) -> None: ...
    def update(self, data: Buffer) -> None: ...
    def digest(self) -> bytes: ...
    def hexdigest(self) -> str: ...
    def copy(self) -> RIPEMD160Hash: ...
    def new(self, data: Buffer = ...) -> RIPEMD160Hash: ...

def new(data: Buffer = ...) -> RIPEMD160Hash: ...
digest_size: int
block_size: int
