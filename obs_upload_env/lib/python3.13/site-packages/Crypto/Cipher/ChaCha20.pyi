from typing import Union, overload, Optional

Buffer = bytes|bytearray|memoryview

def _HChaCha20(key: <PERSON><PERSON><PERSON>, nonce: <PERSON>uffer) -> bytearray: ...

class ChaCha20Cipher:
    block_size: int
    nonce: bytes

    def __init__(self, key: <PERSON>uffer, nonce: <PERSON>uffer) -> None: ...
    @overload
    def encrypt(self, plaintext: <PERSON>uff<PERSON>) -> bytes: ...
    @overload
    def encrypt(self, plaintext: <PERSON>uff<PERSON>, output: Union[bytearray, memoryview]) -> None: ...
    @overload
    def decrypt(self, plaintext: Buffer) -> bytes: ...
    @overload
    def decrypt(self, plaintext: Buffer, output: Union[bytearray, memoryview]) -> None: ...
    def seek(self, position: int) -> None: ...

def new(key: Buffer, nonce: Optional[Buffer] = ...) -> ChaCha20Cipher: ...

block_size: int
key_size: int
