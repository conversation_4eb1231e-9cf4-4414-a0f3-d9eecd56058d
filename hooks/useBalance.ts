import { useSelector } from "react-redux";
import { IAppState, SUPPORT_WALLET_ENUM } from "../constant/type";
import { useEffect, useMemo, useState } from "react";
import useNow from "./useNow";
import { satsToBtc } from "../utils";

export default function useBalance(autoRefresh?: boolean) {
  const { btcWallet, btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const [balance, setBalance] = useState<string>('0')
  const getBalance = async () => {
    if (!btcAddress) {
      return
    }
    if (btcWallet === SUPPORT_WALLET_ENUM.unisat && window.unisat) {
      const balance = await window.unisat.getBalance()
      setBalance(satsToBtc(balance?.confirmed || '0'))
    }
  }
  const now = useNow()

  // 监听钱包地址和钱包类型
  useEffect(() => {
    getBalance()
  }, [btcWallet, btcAddress])

  // 监听当前时间
  useEffect(() => {
    if (now % 15 === 0 && autoRefresh) {
      getBalance()
    }
  }, [now, autoRefresh])
  return {
    balance,
    getBalance
  }
}
