import Image from "next/image";
import styled from "styled-components";
import TickSelect from "./TickSelect";
import { useEffect, useMemo, useState } from "react";
import { getRedPacketTickAnchorAmount } from "@/server";
import toast from "react-hot-toast";

const Items = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 10px;
  position: relative;
  .max-value {
    position: absolute;
    right: 0;
    bottom: -12px;
    font-size: 12px;
    color: #a58061;
  }
  .dot {
    color: #542d00;
    font-size: 20px;
  }

  .tab-1-value,
  .tab-2-value,
  .tab-3-value {
    color: #fff;
    font-size: 20px;
    font-weight: bold;
    border-top: 3px solid #8c8475;
    box-shadow: 0 0px 4px rgba(0, 0, 0, 0.1) inset;
    min-width: 200px;
    height: 35px;
    background: #c2b8a2;
    border-radius: 10px;
    display: flex;
    align-items: end;
    justify-content: end;
    position: relative;
    /* flex: 1; */

    input:focus {
      outline: none;
    }
    &:has(input:focus) {
      border: 2px solid #fc7922;
    }

    input {
      /* width: 100%; */
      width: 240px;
      height: 90%;
      text-align: right;
      background: transparent;
      border: none;
      color: inherit;
      font-size: inherit;
      font-weight: inherit;
      outline: none;

      &::placeholder {
        color: rgba(255, 255, 255, 0.7);
        font-size: 16px;
      }

      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      &[type="number"] {
        -moz-appearance: textfield;
      }
    }
  }

  .tab-1-value {
    /* flex: 1; */
  }

  .tab-2-value {
    /* flex: 1; */
  }

  .tab-3-value {
    /* flex: 1; */
  }
`;

interface TabContentProps {
  tabId: number;
  coinAmount: string;
  onCoinAmountChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  birthdayAmount: string;
  onBirthdayAmountChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  coinAmount2: string;
  onCoinAmount2Change: (e: React.ChangeEvent<HTMLInputElement>) => void;
  tickList: { balance: string; tick: string }[];
  isSelectedTick?: (tick: {
    balance: string;
    tick: string;
    anchorAmount?: string;
  }) => void;
  players: any[];
}

const TabContent = ({
  tabId,
  coinAmount,
  onCoinAmountChange,
  birthdayAmount,
  onBirthdayAmountChange,
  coinAmount2,
  onCoinAmount2Change,
  tickList,
  isSelectedTick,
  players,
}: TabContentProps) => {
  // 独立状态
  const [selectedTickTab1, setSelectedTickTab1] = useState<{
    balance: string;
    tick: string;
    anchorAmount?: string;
  }>({ ...tickList[0], anchorAmount: undefined });
  const [selectedTickTab2, setSelectedTickTab2] = useState<{
    balance: string;
    tick: string;
    anchorAmount?: string;
  }>({ ...tickList[0], anchorAmount: undefined });
  const [loadingTab1, setLoadingTab1] = useState(false);
  const [loadingTab2, setLoadingTab2] = useState(false);

  // tab切换时重置对应tab的状态
  useEffect(() => {
    if (tabId === 1) {
      setSelectedTickTab1({ ...tickList[0] });
      onCoinAmountChange({
        target: { value: "" },
      } as React.ChangeEvent<HTMLInputElement>);
    } else if (tabId === 2) {
      setSelectedTickTab2({ ...tickList[0] });
      onCoinAmount2Change({
        target: { value: "" },
      } as React.ChangeEvent<HTMLInputElement>);
      // onBirthdayAmountChange({
      //   target: { value: birthdayAmount },
      // } as React.ChangeEvent<HTMLInputElement>);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tabId, tickList]);

  // 选中tick变化时的副作用（只对当前tab生效）
  useEffect(() => {
    if (tabId === 1) {
      isSelectedTick?.(selectedTickTab1);
      if (players) {
        const anchorAmount = Number(selectedTickTab1.anchorAmount) || 1;
        const res = players.length * anchorAmount;
        onCoinAmountChange({
          target: {
            value: res.toString(),
          },
        } as React.ChangeEvent<HTMLInputElement>);
      }
    } else if (tabId === 2) {
      isSelectedTick?.(selectedTickTab2);
      if (players) {
        const anchorAmount = Number(selectedTickTab2.anchorAmount) || 1;
        onCoinAmount2Change({
          target: {
            value: (Number(birthdayAmount) * anchorAmount).toString(),
          },
        } as React.ChangeEvent<HTMLInputElement>);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tabId, selectedTickTab1, selectedTickTab2, players, birthdayAmount]);

  // 保留8位小数
  function truncateTo8Decimals(val: string): string {
    if (!val.includes(".")) return val;
    const [int, dec] = val.split(".");
    return dec.length > 8 ? `${int}.${dec.slice(0, 8)}` : val;
  }

  // 获取anchorAmount（独立tab）
  const getAnchorAmount = async (
    opt: { balance: string; tick: string; anchorAmount?: string },
    tab: 1 | 2
  ) => {
    if (tab === 1) setLoadingTab1(true);
    else setLoadingTab2(true);
    try {
      const res = await getRedPacketTickAnchorAmount(opt.tick);
      const { code, msg, data } = res.data;
      if (code === 1) {
        const params = {
          ...opt,
          anchorAmount: data.anchorAmount,
        };
        if (tab === 1) setSelectedTickTab1(params);
        else setSelectedTickTab2(params);
      } else {
        toast.error(msg);
      }
    } catch (error) {
      console.log(error);
    } finally {
      if (tab === 1) setLoadingTab1(false);
      else setLoadingTab2(false);
    }
  };

  // maxBalance分别计算
  const maxBalanceTab1 = useMemo(() => {
    const balance = Number(truncateTo8Decimals(selectedTickTab1.balance));
    return balance;
  }, [selectedTickTab1]);
  const maxBalanceTab2 = useMemo(() => {
    const balance = Number(truncateTo8Decimals(selectedTickTab2.balance));
    return balance;
  }, [selectedTickTab2]);

  return (
    <>
      {tabId === 1 && (
        <div className="tab-1-content">
          <Items>
            <div className="icon">
              <TickSelect
                list={tickList}
                onChange={async (opt) => {
                  setSelectedTickTab1(opt);
                  await getAnchorAmount(opt, 1);
                }}
              />
            </div>
            <div className="dot">*</div>
            <div className="tab-1-value">
              {/* {loadingTab1 ? (
                <span>...</span>
              ) : (
                <input
                  type="number"
                  value={truncateTo8Decimals(coinAmount)}
                  onChange={(e) => {
                    const truncated = truncateTo8Decimals(e.target.value);
                    onCoinAmountChange({
                      ...e,
                      target: { ...e.target, value: truncated },
                    });
                  }}
                  placeholder="0"
                />
              )} */}
              <input
                type="number"
                value={loadingTab1 ? "..." : truncateTo8Decimals(coinAmount)}
                onChange={(e) => {
                  const truncated = truncateTo8Decimals(e.target.value);
                  onCoinAmountChange({
                    ...e,
                    target: { ...e.target, value: truncated },
                  });
                }}
                placeholder={loadingTab1 ? "..." : "Please enter the quantity"}
              />
            </div>
            <span className="max-value">max {maxBalanceTab1}</span>
          </Items>
        </div>
      )}
      {tabId === 2 && (
        <div className="tab-2-content">
          <ul className="tab-2-content-list">
            <li className="tab-2-content-item">
              <Items>
                <div className="icon">
                  <Image
                    src="/image/chatRoom/gift.png"
                    alt="gift"
                    width={48}
                    height={48}
                  />
                </div>
                <div className="dot">*</div>
                <div className="tab-2-value">
                  <input
                    type="number"
                    min={1}
                    max={99}
                    value={birthdayAmount}
                    onChange={(e) => {
                      let v = e.target.value;
                      // 只允许数字，空字符串或0都变为1
                      if (v === "" || v === "0") {
                        onBirthdayAmountChange({
                          ...e,
                          target: { ...e.target, value: "1" },
                        });
                        return;
                      }
                      let num = Number(v);
                      if (num < 1) num = 1;
                      if (num > 99) num = 99;
                      onBirthdayAmountChange({
                        ...e,
                        target: { ...e.target, value: num.toString() },
                      });
                    }}
                    placeholder="Please enter the quantity"
                  />
                </div>
              </Items>
            </li>
            <li className="tab-2-content-item">
              <Items>
                <div className="icon">
                  <TickSelect
                    list={tickList}
                    onChange={async (opt) => {
                      setSelectedTickTab2(opt);
                      await getAnchorAmount(opt, 2);
                    }}
                  />
                </div>
                <div className="dot">*</div>
                <div className="tab-3-value">
                  {/* {loadingTab2 ? (
                    <span>...</span>
                  ) : (
                    <input
                      type="number"
                      value={truncateTo8Decimals(coinAmount2)}
                      onChange={(e) => {
                        const truncated = truncateTo8Decimals(e.target.value);
                        onCoinAmount2Change({
                          ...e,
                          target: { ...e.target, value: truncated },
                        });
                      }}
                      placeholder="0"
                    />
                  )} */}
                  <input
                    type="number"
                    value={
                      loadingTab2 ? "..." : truncateTo8Decimals(coinAmount2)
                    }
                    onChange={(e) => {
                      const truncated = truncateTo8Decimals(e.target.value);
                      onCoinAmount2Change({
                        ...e,
                        target: { ...e.target, value: truncated },
                      });
                    }}
                    placeholder={
                      loadingTab2 ? "..." : "Please enter the quantity"
                    }
                  />
                </div>
                <span className="max-value">max {maxBalanceTab2}</span>
              </Items>
            </li>
          </ul>
        </div>
      )}
    </>
  );
};

export default TabContent;
