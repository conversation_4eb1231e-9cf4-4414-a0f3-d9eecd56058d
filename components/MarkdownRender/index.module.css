.markdownContent {
  color: #333;
  margin: 0 auto;
}

.markdownContent h1 {
  border-bottom: 1px solid #eaecef;
  margin: 0;
}

.markdownContent h2 {
  border-bottom: 1px solid #eaecef;
  margin: 0;

}

.markdownContent h3 {
  /* font-size: 1.5em; */
  margin: 0;

}

.markdownContent h4 {
  /* font-size: 1.25em; */
  margin: 0;

}

.markdownContent h5 {
  /* font-size: 1em; */
  margin: 0;

}

.markdownContent h6 {
  /* font-size: 0.85em; */
  color: #6a737d;
  margin: 0;

}

.markdownContent blockquote {
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
  margin: 0;
}

.markdownContent pre {
  background-color: #f6f8fa;
  border-radius: 3px;
  padding: 16px;
  overflow: auto;
}

.markdownContent code {
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, 'Liberation Mono', <PERSON><PERSON>, monospace;
  font-size: 85%;
  padding: 0.2em 0.4em;
}

.markdownContent pre code {
  background-color: transparent;
  padding: 0;
}

.markdownContent table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

.markdownContent table th,
.markdownContent table td {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
  text-align: center;
}

.markdownContent table tr {
  background-color: #fff;
  border-top: 1px solid #c6cbd1;
}

.markdownContent table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

.markdownContent img {
  max-width: 100%;
}

.markdownContent ul,
.markdownContent ol {
  padding-left: 2em;
}

.markdownContent p {
  margin: 0;
}