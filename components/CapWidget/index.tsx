// components/CapWidget.tsx
import Script from "next/script";
import { useEffect, useMemo } from "react";

interface ICapWidgetProps {
  onSolve: (token: string) => void;
  isKey?: string;
}

export default function CapWidget({ onSolve, isKey }: ICapWidgetProps) {
  // const isKey = key ? key : "d3b88b88022d"
  // const CAP_API_ENDPOINT = `https://captchas.satworld.xyz/${isKey}/api/`

  const CAP_API_ENDPOINT = useMemo(() => {
    return `https://captchas.satworld.xyz/${isKey ? isKey : "9896a5681c3a"}/api/`;
  }, [isKey]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const capWidget = document.getElementById("cap");
      if (capWidget) {
        capWidget.addEventListener("solve", (e: any) => {
          if (e && e.detail && e.detail.token) {
            const token = e.detail.token;
            onSolve(token);
          }
        });
      }
    }
  }, []);

  return (
    <>
      <Script
        src="https://cdn.jsdelivr.net/npm/@cap.js/widget@0.1.17/cap.min.js"
        strategy="afterInteractive"
      />
      <cap-widget
        id="cap"
        data-cap-api-endpoint={CAP_API_ENDPOINT}
      ></cap-widget>
    </>
  );
}
