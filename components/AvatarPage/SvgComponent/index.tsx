import {SvgComponentView} from "./style";

export default function SvgComponent({svg, className, onMouseLeave, onMouseEnter, onClick}: { svg: string, className?: string, onMouseEnter?: () => void, onMouseLeave?: () => void, onClick?: () => void }) {
  return <SvgComponentView dangerouslySetInnerHTML={{__html: svg}} className={className} onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave} onClick={onClick}/>
}
