import styled from "styled-components";
import {THEME_MEDIA_ENUM} from "../../constant";

export const AvatarPageView = styled.div`
  width: 100vw;
  height: 100vh;
  display: flex;
  overflow: hidden;
  #render-target {
    flex: 1;
    height: 100%;
    width: 100%;
    background: transparent !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &>.loading-popup{
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 10;
    left: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #cccccc;
    &>p{
      font-family: JetBrainsMono;
      font-size: 16px;
      font-weight: 400;
      line-height: 19.36px;
      text-align: center;
      color: #615A57;
      margin: 0;
    }
  }
`

export const StorageMenuView = styled.div`
  display: none;
  ${THEME_MEDIA_ENUM.FRONTEND_LARGE}{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 32px;
    position: fixed;
    left: 32px;
    top: 50%;
    transform: translate(0, -50%);
    .storage-menu-item{
      width: 72px;
      height: 72px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0px 0px 16px 0px #00000040;
      background: #140F08;
      border-radius: 16px;
      cursor: pointer;
      &.active{
        background: #FF8316;
        box-shadow: 0px 0px 8px 0px #00000040 inset;
      }
      .edit-paths-icon{
        width: 52px;
        height: 48px;
      }
      .edit-scene-icon{
        width: 48px;
        height: 48px;
      }
    }
  }
`
