import styled from "styled-components";
import {THEME_MEDIA_ENUM} from "../../../constant";

export const MenuView = styled.div<{ initAni: boolean, isFrontend?: boolean }>`
  position: fixed;
  width: 288px;
  height: auto;
  max-height: calc(${({isFrontend}) => isFrontend ? '100% - 210px' : '90%'});
  background: #FAFAFA99;
  top: 50%;
  left: 0;
  border-top-right-radius: 32px;
  border-bottom-right-radius: 32px;
  transform: translate(-100%, -50%);
  z-index: 3;
  backdrop-filter: blur(4px);
  .top-menu-tabs {
    width: 100%;
    overflow-x: auto;
    display: flex;
    gap: 21px;
    padding: 16px 16px 8px 16px;
    box-sizing: border-box;
    overflow-y: hidden;
    border-top-right-radius: 32px;
    height: 64px;
    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      background: #00000010;
      border-radius: 8px;
    }

    &::-webkit-scrollbar-thumb {
      background: #000;
      border-radius: 8px;
    }

    & > div {
      background: #AA9D92;
      box-shadow: 0px 4px 4px 0px #00000040 inset;
      border-radius: 12px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      min-width: 48px;
      height: 40px;

      & > img {
        width: 24px;
        height: 24px;
        opacity: 0.5;
      }

      &.active {
        background: #FF7600;
        &>img{
          opacity: 1;
        }
      }
    }
  }

  .right-menu-tabs {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translate(100%, -50%);
    display: flex;
    flex-direction: column;
    gap: 48px;

    .right-menu-item {
      display: flex;
      align-items: center;

      & > div.arrow {
        opacity: 0;
        width: 10px;
        height: 10px;
        margin-left: -0.5px;
      }

      & > .right-icon {
        cursor: pointer;
         opacity: 0.6; 
      }

       &.active{
         &>.right-icon{
           opacity: 1;
         }

         & > div.arrow {
           opacity: 1;
         }
      }
      &:hover{

        &>.right-icon{
          opacity: 1;
        }
      }
    }
  }

  transition: transform ${({initAni}) => initAni ? '0.2s ease-in-out' : 'none'};

  &.show {
    transform: translate(0, -50%);
  }

  ${THEME_MEDIA_ENUM.ORD_BROWSER} {
    width: 205px;
    border-top-right-radius: 16px;
    border-bottom-right-radius: 16px;

    .top-menu-tabs {
      width: 100%;
      gap: 15px;
      padding: 12px 12px 6px 12px;
      border-top-right-radius: 16px;
      height: 58px;
      // 自定义滚动条
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
        border-radius: 6px;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 6px;
      }

      & > div {
        width: 34px;
        min-width: 34px;
        height: 34px;

        & > img {
          width: 24px;
          height: 24px;
        }
      }
    }

    .right-menu-tabs {
      gap: 25px;

      .right-menu-item {
        & > img {
          width: 36px;
          height: 36px;
          cursor: pointer;
        }
      }
    }

  }

  ${THEME_MEDIA_ENUM.ORD_PREVIEW} {
    display: none;
  }

  ${THEME_MEDIA_ENUM.FRONTEND_LARGE} {
    left: 25%;
    top: 50%;
    border-radius: 32px;
    transform: translate(calc(-50%), -50%) !important;
  }
`
export const MenuContentView = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 8px 16px 8px 16px;
  box-sizing: border-box;
  height: calc(120px * 4 + 16px); //放4行
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background: #00000010;
    border-radius: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: #53515D;
    border-radius: 8px;
  }

  .loading-view {
    text-align: center;
    padding: 20px 0;
    color: #00000050;
  }

  .empty-box, .loading-box {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: -32px;

    & > p {
      font-family: JetBrainsMono;
      font-size: 16px;
      font-weight: 400;
      line-height: 19.36px;
      text-align: center;
      color: #615A57;
      margin: 8px 0 0 0;
    }

  }

  .coming-soon {

  }

  & > .select-spin {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 16px;

    & > div {
      height: 120px;
      background: #00000040;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-sizing: border-box;
      border: 4px solid transparent;

      & > img {
        width: 100%;
        height: 100%;
        border-radius: 20px;
      }

      & > span {
        color: #ffffff;
      }

      &.active {
        border: 4px solid #FF8316;
      }
      &.disabled{
        cursor: not-allowed;
        filter: grayscale(100%);
      }
    }
  }

  ${THEME_MEDIA_ENUM.ORD_BROWSER} {
    padding: 6px 12px 6px 12px;
    height: calc(85px * 4 + 12px);

    & > .select-spin {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 12px;

      & > div {
        height: 85px;
        border-radius: 10px;
        border: 2px solid transparent;

        &.active {
          border: 2px solid #FF8316;
        }
      }
    }
  }
`
