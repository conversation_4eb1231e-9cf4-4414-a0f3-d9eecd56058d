import styled from "styled-components";
import {THEME_MEDIA_ENUM} from "../../../constant";

export const InfoPanelBox = styled.div<{ initAni: boolean }>`
  position: fixed;
  right: 0;
  top: 50%;
  transform: translate(100%, -50%);
  transition: transform ${({initAni}) => initAni ? '0.2s ease-in-out' : 'none'};
  max-height: 100%;
  overflow-y: auto;
  padding: 20px 0;
  box-sizing: border-box;
  z-index: 3;
  &.show {
    transform: translate(0, -50%);
  }
  &>div{
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: end;
  }
  ${THEME_MEDIA_ENUM.ORD_BROWSER}{
    &>div{
      padding-right: 10px;
    }
  }
  ${THEME_MEDIA_ENUM.FRONTEND_LARGE}{
    right: 25%;
    top: 150%;
    transform: translate(50%, -50%);
    display: inline-flex;
    transition: top 0.2s ease-in-out;
    &.show{
      top: 50%;
      transform: translate(50%, -50%);
    }
  }
  
`

export const InfoPanelView = styled.div`
  width: 271px;
  background: #FAFAFA80;
  backdrop-filter: blur(4px);
  border-radius: 32px;
  padding: 24px;
  box-sizing: border-box;
  flex-shrink: 0;
  & > h2 {
    font-family: JetBrainsMonoBold;
    font-size: 22px;
    line-height: 26px;
    text-align: right;
    color: #140F08;
    padding: 0;
    margin: 0;
  }

  .info-panel-content {
    margin-top: 24px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: end;
    overflow: hidden;

    & > p {
      width: 100%;
      padding: 0;
      margin: 0;
      font-family: JetBrainsMono;
      font-size: 16px;
      font-weight: 400;
      line-height: 20px;
      text-align: right;
      color: #686663;
      display: flex;
      flex-wrap: wrap;
      justify-content: end;
      /* color: #140F0850; */
      //white-space: nowrap;
      //overflow: hidden;
      //text-overflow: ellipsis;
      &>span{
        white-space: nowrap;
      }
    }
  }

  ${THEME_MEDIA_ENUM.ORD_BROWSER}{
    width: 180px;
    min-height: auto;
    border-radius: 12px;
    padding: 12px;

    & > h2 {
      font-size: 16px;
      line-height: 22px;
    }
    .info-panel-content {
      margin-top: 10px;
      display: flex;
      flex-direction: column;
      gap: 8px;

      & > p {
        font-size: 14px;
        line-height: 16px;
      }
    }
  }
`
export const InfoPanelEditView = styled.div`
  width: 271px;
  background: #FAFAFA80;
  backdrop-filter: blur(4px);
  border-radius: 32px;
  padding: 24px;
  box-sizing: border-box;
  flex-shrink: 0;
  margin-top: 16px;
  .set-title{
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 10px;
    &>span{
      font-family: JetBrainsMonoBold;
      font-size: 18px;
      line-height: 21.78px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #686663;
    }
  }

  &>.set-panel-box{
    margin-top: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    &>.color-set,.texture-set{
      display: flex;
      align-items: center;
      justify-content: end;
      gap: 8px;
      &>span{
        font-family: JetBrainsMono;
        font-size: 16px;
        font-weight: 400;
        line-height: 20px;
        text-align: right;
        color: #686663;
      }
      &>.color-value{
        display: flex;
        align-items: center;
        height: 28px;
        box-sizing: border-box;
        gap: 4px;
        background: #ffffff30;
        border-radius: 4px;
        padding: 4px;
        cursor: pointer;

        &>div{
          width: 20px;
          height: 20px;
          border-radius: 2px;
        }
        &>span{
          font-family: JetBrainsMono;
          font-size: 20px;
          font-weight: 400;
          line-height: 20px;
          color: #686663;
        }
        &.active{
          border: 1px solid #140F08;
          &>span{
            color: #140F08;
          }
        }
      }
      &>.texture-value{
        width: 28px;
        height: 28px;
        background: #FFFFFF1A;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        box-sizing: border-box;
        cursor: pointer;
        &>img{
          width: 20px;
          height: 20px;
        }
        &.active{
          border: 1px solid #140F08;
        }
      }
    }
  }
  .empty-box{
    width: 28px;
    height: 28px;
    background: #FAFAFA80;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    box-sizing: border-box;
    cursor: pointer;
    &.active{
      border: 1px solid #140F08;
    }
  }
  .eyes-icon{
    cursor: pointer;
  }
  ${THEME_MEDIA_ENUM.ORD_BROWSER} {
    width: 180px;
    min-height: auto;
    border-radius: 12px;
    padding: 12px;
    margin-top: 5px;
    &>.set-title{
      &>span{
        font-size: 16px;
      }
    }
    &>.set-panel-box{
      margin-top: 8px;
      &>.color-set,.texture-set{
        gap: 8px;
        &>span{
          font-size: 14px;
          line-height: 16px;
        }
        &>.color-value{
          display: flex;
          align-items: center;
          height: 24px;
          padding: 4px;

          &>div{
            width: 16px;
            height: 16px;
          }
          &>span{
            font-size: 14px;
            line-height: 16px;
          }
        }
        &>.texture-value{
          width: 24px;
          height: 24px;
          &>img{
            width: 16px;
            height: 16px;
          }
          &.active{
            border: 1px solid #ffffff;
            color: #FFFFFF;
          }
        }
        &>.empty-icon{
          margin-left: 0px;
        }
      }
    }
    .empty-box{
      width: 24px;
      height: 24px;
      cursor: pointer;
      svg{
        width: 16px;
        height: 16px;
      }
    }
  }
`
export const SelectColorPanelView = styled.div<{show: boolean}>`
  width: 304px;
  max-height: ${({ show }) => show ? '500px': '0'};
  transition: 0.2s linear;
  overflow: hidden;
  margin-top: 16px;
  flex-shrink: 0;
  .select-color-panel{
    background: #FAFAFA80;
    backdrop-filter: blur(4px);
    border: 1px solid #fff;
    border-radius: 32px;
    box-sizing: border-box;
    padding: 24px;
    .react-colorful__pointer{
      width: 24px;
      height: 24px;
    }
    .select-color-btn{
      margin-top: 16px;
      width: 100%;
      height: 28px;
      background: #FFFFFF1A;
      border: 1px solid #140F08;
      border-radius: 4px;
      box-sizing: border-box;
      padding: 4px;
      display: flex;
      align-items: center;
      &>div{
        width: 20px;
        height: 20px;
        border-radius: 2px;
      }
      &>span{
        flex: 1;
        text-align: center;
        font-family: JetBrainsMono;
        font-size: 20px;
        font-weight: 400;
        line-height: 20px;
        color: #140F08;
      }
    }
    .action-btns{
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 16px;
      margin-top: 16px;
      &>div{
        display: flex;
        align-items: center;
        justify-content: center;
        height: 36px;
        background: #fff;
        border-radius: 4px;
      }
    }
  }
  ${THEME_MEDIA_ENUM.ORD_BROWSER}{
    width: 210px;
    margin-top: 5px;
    .select-color-panel{
      border-radius: 12px;
      padding: 12px;
      .react-colorful__pointer{
        width: 18px;
        height: 18px;
      }
      .react-colorful__alpha, .react-colorful__hue{
        height: 20px;
      }
      .select-color-btn{
        margin-top: 12px;
        height: 22px;
        padding: 4px;
        &>div{
          width: 16px;
          height: 16px;
        }
        &>span{
          font-size: 16px;
          line-height: 18px;
        }
      }
      .action-btns{
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 12px;
        margin-top: 12px;
        &>div{
          height: 32px;
          &>img{
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }
`
export const SelectTexturePanelView = styled.div<{show: boolean}>`
  width: 304px;
  max-height: ${({ show }) => show ? '500px': '0'};
  transition: 0.2s linear;
  overflow: hidden;
  flex-shrink: 0;
  /* margin-top: 16px; */
  .select-texture-panel{
    background: #FAFAFA80;
    backdrop-filter: blur(4px);
    border: 1px solid #fff;
    border-radius: 32px;
    box-sizing: border-box;
    padding: 24px;
    &>h2{
      font-family: JetBrainsMonoBold;
      font-size: 20px;
      line-height: 20px;
      text-align: center;
      color: #140F08;
      margin: 0;
    }
    &>input{
      width: 100%;
      margin-top: 16px;
      border: 1px solid #140F0866;
      border-radius: 16px;
      height: 48px;
      background: #FAFAFA80;
      font-family: JetBrainsMono;
      font-size: 20px;
      font-weight: 400;
      line-height: 24.2px;
      color: #140F08;
      padding: 0 16px;
      box-sizing: border-box;
      outline: none;
      &::placeholder{
        color: #140F0866;
      }
    }
    .texture-show{
      margin-top: 16px;
      width: 100%;
      height: 256px;
      display: flex;
      align-items: center;
      justify-content: center;
      &>.loading-view{
        display: flex;
        align-items: center;
        gap: 24px;
        &>div{
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #ffffff;
          opacity: 0.4;
          &:nth-child(1){
            opacity: 1;
          }
        }
      }
      &>img{
        width: 200px;
      }
    }
    .action-btns{
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 16px;
      margin-top: 16px;
      &>div{
        display: flex;
        align-items: center;
        justify-content: center;
        height: 36px;
        border-radius: 4px;
        background: #fff;
        &>img{
          width: 24px;
          height: 24px;
        }
      }
    }
  }
  ${THEME_MEDIA_ENUM.ORD_BROWSER}{
    width: 210px;
    margin-top: 5px;
    .select-texture-panel{
      border-radius: 12px;
      padding: 12px;
      &>h2{
        font-size: 16px;
        line-height: 16px;
      }
      &>input{
        margin-top: 12px;
        border-radius: 12px;
        height: 36px;
        font-size: 14px;
        line-height: 14px;
        padding: 0 12px;
      }
      .texture-show{
        margin-top: 12px;
        height: 184px;
        &>.loading-view{
          display: flex;
          align-items: center;
          gap: 18px;
          &>div{
            width: 10px;
            height: 10px;
          }
        }
        &>img{
          width: 100%;
        }
      }
      .action-btns{
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 12px;
        margin-top: 12px;
        &>div{
          height: 32px;
          &>img{
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }
`
