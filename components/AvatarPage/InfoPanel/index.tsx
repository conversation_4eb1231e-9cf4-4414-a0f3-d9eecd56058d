import {InfoPanelBox, InfoPanelEditView, InfoPanelView, SelectColorPanelView, SelectTexturePanelView} from "./style";
import {IAvatarMetadata, IFatherInscription} from "../../../constant/type";
import {useEffect, useMemo, useRef, useState} from "react";
import {HexColorPicker} from "react-colorful";
import {getNFTImgLink, toFormatStr} from "../../../utils";
import {IS_LOCAL_TEST, ORD_NFT_IMG_SERVER, ORD_SERVER} from "../../../constant";
import {SVG_FILE} from "../../../constant/staticFile";
import SvgComponent from "../SvgComponent";

interface IProps {
  userAvatarMetadata: IAvatarMetadata,
  cacheUserAvatarMetadata: IAvatarMetadata,
  globalParts: IFatherInscription[],
  activeTab: number,
  initAni: boolean,
  isShowMenu: boolean,
  onUpdate: Function
}

enum EDIT_TYPE {
  Color = 'Color',
  Texture = 'Texture',
}

export default function InfoPanel({userAvatarMetadata, globalParts, activeTab, initAni, isShowMenu, onUpdate, cacheUserAvatarMetadata}: IProps) {
  const partData: IFatherInscription|undefined = globalParts[activeTab]
  const [editType, setEditType] = useState<EDIT_TYPE | null>(null)


  const showData = useMemo(() => {
    if (partData && userAvatarMetadata[partData.pathIdKey]) {
      return partData.childrenInscription.find((item) => item.inscriptionId === userAvatarMetadata[partData.pathIdKey])
    }
    return null
  }, [userAvatarMetadata, globalParts, activeTab, JSON.stringify(partData||{})])
  useEffect(() => {
    if (!showData) {
      setEditType(null)
    }
  }, [showData])
  useEffect(() => {
    setEditType(null)
  }, [showData?.inscriptionId])


  const showColorSet = partData && !!partData.color
  const showTextureSet = partData && !!partData.texture
  // @ts-ignore
  const colorValue = partData && partData.color ? userAvatarMetadata[partData.color.pathIdKey]||'' : ''
  const activeEditColor = editType === EDIT_TYPE.Color
  // @ts-ignore
  const textureValue = partData && partData.texture ? userAvatarMetadata[partData.texture.pathIdKey]||'' : ''
  const activeEditTexture = editType === EDIT_TYPE.Texture

  const onCloseEditPanel = () => {
    setEditType(null)
  }

  // Color眨眼睛
  const [closeColorEyes, setCloseColorEyes] = useState<boolean>(false)
  // Color输入备份
  const [colorBackup, setColorBackup] = useState<string>('')
  // Texture眨眼睛
  const [closeTextureEyes, setCloseTextureEyes] = useState<boolean>(false)
  // Texture输入备份
  const [textureBackup, setTextureBackup] = useState<string>('')
  useEffect(() => {
    setColorBackup(cacheUserAvatarMetadata.shirtColor)
    setTextureBackup(cacheUserAvatarMetadata.shirtTextureId)
  }, [cacheUserAvatarMetadata])

  return <InfoPanelBox initAni={initAni} className={isShowMenu && showData ? 'show' : ''}>
    <div>
      <InfoPanelView>
        <h2>{partData?.type}</h2>
        <div className="info-panel-content">
          <p>Collection: <span>{showData && showData.metadata.collection}</span></p>
          <p>Version: {showData && showData.metadata.version}</p>
          <p>Inscription ID: {showData && toFormatStr(showData.inscriptionId, 4, 4)}</p>
        </div>
      </InfoPanelView>
      {
        (showColorSet || showTextureSet) && <InfoPanelEditView>
          <div className="set-title">
            <SvgComponent svg={SVG_FILE.setIcon}/>
            <span>Customized</span>
          </div>
          <div className="set-panel-box">
            {
              showColorSet && <div className="color-set">
                <span>Color: </span>
                {
                  colorValue ? <div className={'color-value' + (activeEditColor ? ' active' : '')}
                                    onClick={() => !closeColorEyes && setEditType(editType === EDIT_TYPE.Color ? null : EDIT_TYPE.Color)}>
                    <div style={{backgroundColor: colorValue || 'transparent'}}/>
                    <span>{colorValue}</span>
                  </div> : <div className={"empty-box" + (activeEditColor ? ' active' : '')}
                                onClick={() => !closeColorEyes && setEditType(editType === EDIT_TYPE.Color ? null : EDIT_TYPE.Color)}>
                    <SvgComponent svg={SVG_FILE.emptyIcon} className="empty-icon"/>
                  </div>
                }
                {/*<SvgComponent svg={SVG_FILE.eyesSvg} className="eyes-icon" onClick={() => setEditType(editType === EDIT_TYPE.Color ? null : EDIT_TYPE.Color)}/>*/}
                <SvgComponent svg={closeColorEyes ? SVG_FILE.eyesCloseSvg : SVG_FILE.eyesSvg} className="eyes-icon" onClick={() => {
                  const pathIdKey = partData?.color?.pathIdKey
                  if (!pathIdKey){
                    return
                  }
                  if (closeColorEyes){
                    setEditType(null)
                    onUpdate(pathIdKey, colorBackup)
                    setCloseColorEyes(false)
                  } else {
                    setEditType(null)
                    onUpdate(pathIdKey, cacheUserAvatarMetadata.shirtColor)
                    setCloseColorEyes(true)
                  }
                }}/>
              </div>
            }
            {
              showTextureSet && <div className="texture-set">
                <span>Texture: </span>
                {
                  textureValue ? <div className={'texture-value' + (activeEditTexture ? ' active' : '')}
                                      onClick={() => !closeTextureEyes && setEditType(editType === EDIT_TYPE.Texture ? null : EDIT_TYPE.Texture)}>
                    <img src={getNFTImgLink(textureValue)} alt=""/>
                  </div> : <div className={"empty-box" + (activeEditTexture ? ' active' : '')}
                                onClick={() => !closeTextureEyes && setEditType(editType === EDIT_TYPE.Texture ? null : EDIT_TYPE.Texture)}>
                    <SvgComponent svg={SVG_FILE.emptyIcon} className="empty-icon"/>
                  </div>
                }
                <SvgComponent svg={closeTextureEyes ? SVG_FILE.eyesCloseSvg : SVG_FILE.eyesSvg} className="eyes-icon" onClick={() => {
                  const pathIdKey = partData?.texture?.pathIdKey
                  if (!pathIdKey){
                    return
                  }
                  if (closeTextureEyes){
                    setEditType(null)
                    onUpdate(pathIdKey, textureBackup)
                    setCloseTextureEyes(false)
                  } else {
                    setEditType(null)
                    // @ts-ignore
                    onUpdate(pathIdKey, cacheUserAvatarMetadata.shirtTextureId)
                    setCloseTextureEyes(true)
                  }

                }}/>
              </div>
            }
          </div>
        </InfoPanelEditView>
      }

      <SelectColorPanel show={editType === EDIT_TYPE.Color} onUpdate={onUpdate} colorValue={colorValue}
                        pathIdKey={partData?.color?.pathIdKey} onCloseEditPanel={onCloseEditPanel} setColorBackup={setColorBackup} closeColorEyes={closeColorEyes}/>
      <SelectTexturePanel show={editType === EDIT_TYPE.Texture} onUpdate={onUpdate}
                          textureValue={textureValue}
                          pathIdKey={partData?.texture?.pathIdKey} onCloseEditPanel={onCloseEditPanel} setTextureBackup={setTextureBackup} closeTextureEyes={closeTextureEyes}/>
    </div>
  </InfoPanelBox>
}

interface IPanelProps {
  show: boolean,
  onUpdate: Function,
  pathIdKey?: string,
  onCloseEditPanel: Function
}

interface IColorPanelProps extends IPanelProps {
  colorValue: string
  setColorBackup: Function
  closeColorEyes: boolean
}

function SelectColorPanel({show, onUpdate, colorValue, pathIdKey, onCloseEditPanel, setColorBackup, closeColorEyes}: IColorPanelProps) {
  const setColor = (color: string) => {
    if (pathIdKey) {
      onUpdate(pathIdKey, color)
      setColorBackup(color)
    }
  }
  return <SelectColorPanelView show={show}>
    <div className="select-color-panel">
      <HexColorPicker color={colorValue||undefined} onChange={setColor} style={{width: '100%'}}/>
      <div className="select-color-btn">
        <div style={{backgroundColor: colorValue || 'transparent'}}/>
        <span>{colorValue}</span>
      </div>
      <div className="action-btns">
        <div onClick={() => {
          setColor('')
          setColorBackup('')
          onCloseEditPanel()
        }}><SvgComponent svg={SVG_FILE.noIcon}/></div>
        <div onClick={() => onCloseEditPanel()}><SvgComponent svg={SVG_FILE.yesIcon}/></div>
      </div>
    </div>
  </SelectColorPanelView>
}

interface ITexturePanelProps extends IPanelProps {
  textureValue: string
  setTextureBackup: Function
  closeTextureEyes: boolean
}

function SelectTexturePanel({show, onUpdate, textureValue, pathIdKey, onCloseEditPanel, setTextureBackup, closeTextureEyes}: ITexturePanelProps) {
  const [inputValue, setInputValue] = useState<string>('')// /assets/Shirt/Texture/texture_Shirt_00.png
  const [textureId, setTextureId] = useState<string>('')
  const [loading, setLoading] = useState<boolean>(false)

  const getTextureId = async () => {
    if (!inputValue) {
      setTextureId('')
      return
    }
    setLoading(true)
    if (IS_LOCAL_TEST) {
      try {
        setTextureId(inputValue)
      } catch (e: any) {
        console.log(e)
        setTextureId('')
      }
    } else {
      try {
        const resp = await fetch(`${ORD_NFT_IMG_SERVER||ORD_SERVER}/r/inscription/${inputValue}`)
        const data = await resp.json();
        const contentType = data.content_type;
        if (contentType.indexOf("image") !== -1) {
          setTextureId(inputValue)
        } else {
          setTextureId('')
        }
      } catch (e: any) {
        setTextureId('')
      }
    }
    setLoading(false)
  }

  const onSetTexture = () => {
    if (pathIdKey) {
      onUpdate(pathIdKey, textureId)
      setTextureBackup(textureId)
    }
    onCloseEditPanel()
  }
  const onClearTexture = () => {
    setTextureId('')
    setInputValue('')
    if (pathIdKey) {
      onUpdate(pathIdKey, '')
    }
    setTextureBackup('')
    onCloseEditPanel()
  }
  const debounceRef: any = useRef(null)
  useEffect(() => {
    if (loading || closeTextureEyes) {
      return
    }
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }
    debounceRef.current = setTimeout(() => {
      getTextureId()
    }, 100)
  }, [inputValue])
  useEffect(() => {
    if (closeTextureEyes){
      return
    }
    setTextureId(textureValue)
    setInputValue(textureValue)
  }, [textureValue, closeTextureEyes])
  return <SelectTexturePanelView show={show}>
    <div className="select-texture-panel">
      <h2>Customize with<br/>Inscription NFT</h2>
      <input type="text" value={inputValue} onChange={(e) => setInputValue(e.target.value)} placeholder="Inscription ID"/>
      <div className="texture-show">
        {
          loading ? <div className="loading-view">
            <div/>
            <div/>
            <div/>
          </div> : textureId ? <img src={getNFTImgLink(textureId)} alt=""/> : null
        }
      </div>
      <div className="action-btns">
        <div onClick={onClearTexture}><SvgComponent svg={SVG_FILE.noIcon}/></div>
        <div onClick={onSetTexture}><SvgComponent svg={SVG_FILE.yesIcon}/></div>
      </div>
    </div>
  </SelectTexturePanelView>
}
