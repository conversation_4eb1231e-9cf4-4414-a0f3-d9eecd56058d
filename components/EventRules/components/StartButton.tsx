import styled, { keyframes } from "styled-components";
import EventBtn from "/public/image/eventBtn.png";

// 定义加载动画
const rotate = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

const ButtonContainer = styled.button<{ isLoading?: boolean }>`
  /* background: #fc7922; */
  background: ${(props) => (props.isLoading ? "rgba(252, 121, 34, 0.8)" : "")};
  color: white;
  padding: 0.7em 1em;
  padding-left: 0.9em;
  display: flex;
  align-items: center;
  border: 1px solid #503829;
  border-radius: 14px;
  overflow: hidden;
  transition: all 0.2s;
  cursor: ${(props) => (props.isLoading ? "not-allowed" : "pointer")};
  margin: 0 auto;
  width: 130px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: JetBrainsMono;
  background-image: url(${EventBtn.src});
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;

  span {
    display: block;
    margin-left: 0.3em;
    transition: all 0.3s ease-in-out;
  }

  svg {
    display: block;
    transform-origin: center center;
    transition: transform 0.3s ease-in-out;
  }

  &:hover .svg-wrapper {
    animation: fly-1 0.6s ease-in-out infinite alternate;
  }

  &:hover svg {
    transform: ${(props) =>
      props.isLoading ? "none" : "translateX(2em) rotate(45deg) scale(1.1)"};
  }

  &:hover span {
    transform: ${(props) => (props.isLoading ? "none" : "translateX(6em)")};
  }

  &:active {
    transform: scale(0.95);
  }

  @keyframes fly-1 {
    from {
      transform: translateY(0.1em);
    }

    to {
      transform: translateY(-0.1em);
    }
  }

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 5px solid #fff;
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation 1s linear infinite;
  }

  @keyframes rotation {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

interface StartButtonProps {
  text: string;
  onClick?: () => void;
  className?: string;
  loading?: boolean;
}

function StartButton({
  text = "Send",
  onClick,
  className,
  loading = false,
}: StartButtonProps) {
  return (
    <ButtonContainer
      onClick={loading ? undefined : onClick}
      className={className}
      isLoading={loading}
      disabled={loading}
    >
      {loading ? (
        <div className="loading-spinner"></div>
      ) : (
        <>
          <div className="svg-wrapper">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              width="24"
              height="24"
            >
              <path fill="none" d="M0 0h24v24H0z"></path>
              <path
                fill="currentColor"
                d="M1.946 9.315c-.522-.174-.527-.455.01-.634l19.087-6.362c.529-.176.832.12.684.638l-5.454 19.086c-.15.529-.455.547-.679.045L12 14l6-8-8 6-8.054-2.685z"
              ></path>
            </svg>
          </div>
          <span>{text}</span>
        </>
      )}
    </ButtonContainer>
  );
}

export default StartButton;
