import {AdoptPotatoPetModalView, PlantingPotatoesView, PotatoBtnView,} from "./style";
import {useEffect, useMemo, useState} from "react";
import {getTempAvatarActivityTime, tempPlantPotato} from "../../../server";
import {useDispatch, useSelector} from "react-redux";
import {IAppState, IPotatoTime, SCENE_TYPE} from "../../../constant/type";
import PotatoImg from "/public/image/header/potato.png";
import useNow from "../../../hooks/useNow";
import Modal from "../../EditAvatarPage/BasicComponents/Modal";
import ModalHeaderImg from "/public/image/header/modal-header.png";
import Potato1Img from "/public/image/header/potato1.png";
import Potato2Img from "/public/image/header/potato2.png";
import Potato3Img from "/public/image/header/potato3.png";
import JumpImg from "/public/image/header/jump.png";
import toast from "react-hot-toast";
import Turnstile, {useTurnstile} from "react-turnstile";
import {TURNSTILE_KEY} from "../../../constant";
import SnowImg from "/public/image/header/snow.png";
import Snow2Img from "/public/image/header/snow2.png";
import GlobalSpaceEvent, {CharacterType, GlobalDataKey, SpaceStatus,} from "../../../world/Global/GlobalSpaceEvent";
import * as THREE from "three";
import {setPotatoTime} from "../../../store/app";
import {AppGameApiKey, GetMyPlayer} from "@/world/Character/MyPlayer";
import {LoadingPageType} from "@/world/Config/DoorConfig";

export default function PlantingPotatoes({
  isClaimPotato,
}: {
  isClaimPotato?: boolean;
}) {
  const { btcAddress, potatoTime, sceneType } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const dispatch = useDispatch();
  const [showModal, setShowModal] = useState<boolean>(false);
  const getData = () => {
    if (!btcAddress) {
      return;
    }
    getTempAvatarActivityTime().then((res) => {
      if (res.data.code === 1) {
        dispatch(setPotatoTime(res.data.data));
      }
    });
  };

  useMemo(() => {
    getData();
  }, [btcAddress]);
  const now = useNow();
  const countDown = useMemo(() => {
    if (potatoTime === null || potatoTime.plantTime === null) {
      return "";
    }
    const d = Math.floor((potatoTime.activityTime - now) / 60 / 60 / 24);
    const h = Math.floor(((potatoTime.activityTime - now) / 60 / 60) % 24);
    const m = Math.floor(((potatoTime.activityTime - now) / 60) % 60);
    return `${d}d ${h}h ${m}m`;
  }, [potatoTime, now]);
  const already = potatoTime !== null && potatoTime.plantTime !== null;

  if (potatoTime === null || !btcAddress || isClaimPotato) {
    return null;
  }
  if (now >= potatoTime.activityTime) {
    if (!potatoTime.plantTime || sceneType === SCENE_TYPE.Island) {
      return null;
    }
    const onGoToHomeland = () => {
      GlobalSpaceEvent.SetDataValue<SpaceStatus>(
        GlobalDataKey.SpaceStatus,
        SpaceStatus.Game
      );
      // TODO
      // 点击引导开门
      const myPlayer = GetMyPlayer()
      myPlayer.callAppApi(AppGameApiKey.setLoaderType, LoadingPageType.Default)
      GlobalSpaceEvent.SetDataValue(GlobalDataKey.TransformData, {
        characterType: CharacterType.Player,
        position: new THREE.Vector3(1, 1, 2.5),
        sceneType: SCENE_TYPE.Island,
      });
    };
    // return (
    //   <PotatoBtnView onClick={onGoToHomeland}>
    //     <span>Go to Homeland</span>
    //   </PotatoBtnView>
    // );
    return null;
  }
  return (
    <>
      <PlantingPotatoesView
        already={already}
        onClick={() => setShowModal(true)}
      >
        <img src={PotatoImg.src} alt="" className="potato-tree" />
        {!already && <img src={SnowImg.src} alt="" className="snow" />}
        {already ? <span>{countDown}</span> : <span>Potato Pet</span>}
      </PlantingPotatoesView>
      <AdoptPotatoPetModal
        showModal={showModal}
        setShowModal={setShowModal}
        getData={getData}
        potatoTime={potatoTime}
        countDown={countDown}
      />
    </>
  );
}

export function AdoptPotatoPetModal({
  showModal,
  setShowModal,
  getData,
  potatoTime,
  countDown,
}: {
  showModal: boolean;
  setShowModal: Function;
  getData: Function;
  potatoTime: IPotatoTime;
  countDown: string;
}) {
  const { btcAddress } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const [loading, setLoading] = useState<boolean>(false);
  const turnstile = useTurnstile();
  const [turnstileToken, setTurnstileToken] = useState<string>("");
  const onTempPlantPotato = () => {
    setLoading(true);
    tempPlantPotato(turnstileToken)
      .then((res) => {
        if (res.data.code === 1) {
          toast.success("Success");
        } else {
          toast.error(res.data.msg);
          turnstile.reset();
        }
        getData();
      })
      .finally(() => {
        setLoading(false);
      });
  };
  useEffect(() => {
    setTurnstileToken("");
  }, [btcAddress]);
  return (
    <Modal
      emptyOnly={true}
      visible={showModal}
      popupClose={true}
      onClose={() => setShowModal(false)}
    >
      <AdoptPotatoPetModalView onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <img src={Snow2Img.src} alt="" className="title-snow" />
          <img src={ModalHeaderImg.src} alt="" className="title-img" />
        </div>
        <div className="plant-view">
          <div className="plant-item">
            <img src={Potato1Img.src} alt="" />
          </div>
          <div className="jump1">
            <img src={JumpImg.src} alt="" />
          </div>
          <div className="plant-item">
            <img src={Potato2Img.src} alt="" />
          </div>
          <div className="jump2">
            <img src={JumpImg.src} alt="" className="jump" />
          </div>
          <div className="plant-item">
            <img src={Potato3Img.src} alt="" />
          </div>
        </div>
        <p className="desc">
          * Once the countdown ends, you can head to the Homeland to adopt your
          Potato Pet.
        </p>
        {potatoTime.plantTime === null && (
          <div className="turnstile-view">
            <Turnstile
              sitekey={TURNSTILE_KEY}
              size="normal"
              onVerify={(token) => {
                setTurnstileToken(token);
              }}
            />
          </div>
        )}
        <div className="action">
          <div className="action-btns">
            <button className="refuse-btn" onClick={() => setShowModal(false)}>
              Refuse
            </button>
            <PotatoBtnView
              onClick={onTempPlantPotato}
              disabled={
                potatoTime.plantTime !== null || loading || !turnstileToken
              }
            >
              {potatoTime.plantTime !== null ? (
                <span>{countDown}</span>
              ) : (
                <span>Adopt</span>
              )}
            </PotatoBtnView>
          </div>
        </div>
      </AdoptPotatoPetModalView>
    </Modal>
  );
}
