import styled from "styled-components";

export const SearchView = styled.div<{isHover: boolean}>`
  width: 280px;
  /* width: 10vw; */
  /* min-width: 100px; */
  /* max-width: 15vw; */
  height: 48px;
  border-radius: 16px;
  border: 1px solid ${({ isHover }) => isHover ? '#7C7C7C' : 'transparent'};
  background: #FAFAFA80;
  box-sizing: border-box;

  display: flex;
  align-items: center;
  transition: all 0.1s;
  padding: 8px 16px;
  box-sizing: border-box;
  position: relative;
  &>input{
    width: 100%;
    flex: 1;
    background: transparent;
    outline: none;
    border: 0;
    color: #140F08;
    font-size: 16px;
    font-weight: 400;
    line-height: 19.36px;
    letter-spacing: 0.02em;
    &::placeholder{
      font-family: JetBrainsMono;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #7C7C7C;
    }
    &.transparent{
      caret-color: transparent;
      &::placeholder{
        color: transparent;
      }
    }
  }
  &>.input-div{
    position: absolute;
    left: 0;
    top: 0;
    width: 210px;
    height: 100%;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding: 0 16px;
    box-sizing: border-box;
    &>span{
      color: #140F08;
      font-size: 16px;
      font-weight: 400;
      line-height: 19.36px;
      // 文字间距
      letter-spacing: 0.02em;
      &.suffix{
        background: #E2AF6E;
      }
    }
  }
  &>.right-tag{
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    border-radius: 8px;
    transition: all 0.1s;
    &.right-tag1{
      width: 32px;
      &>div{
        width: 1px;
        height: 16px;
        background: #615A57;
        transform: rotate(45deg);
      }
    }
    &.right-tag2{
      width: 40px;
      &>span{
        font-family: JetBrainsMono;
        font-size: 14px;
        font-weight: 400;
        line-height: 14px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #140F08;
      }
    }
  }
`
