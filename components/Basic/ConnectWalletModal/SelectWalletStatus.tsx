import { SUPPORT_WALLET_ENUM } from "@/constant/type";
import { AnimatePresence, motion } from "motion/react";
import { memo } from "react";
import styled from "styled-components";

const Text = styled.span`
  @keyframes blinkCursor {
    50% {
      border-right-color: #140F08;
    }
  }

  @keyframes typeAndDelete {
    0%,
    10% {
      width: 0;
    }
    45%,
    55% {
      width: 2.2em;
    } /* adjust width based on content */
    90%,
    100% {
      width: 0;
    }
  }
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  border-right: 0.2em solid green; /* Cursor */
  animation: typeAndDelete 4s steps(11) infinite,
    blinkCursor 0.5s step-end infinite alternate;
`;

interface SelectWalletStatusProps {
  selectedWallet: SUPPORT_WALLET_ENUM;
  connectionStage: "initiate" | "waiting" | "result" | "idle";
  isSuccess: boolean;
  selectedLogo: string;
}

function SelectWalletStatus({
  selectedWallet,
  connectionStage,
  isSuccess,
  selectedLogo,
}: SelectWalletStatusProps) {
  return (
    <motion.div
      key="connecting-state"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="connecting-container"
    >
      <motion.div
        className="logo-container"
        layoutId={
          selectedWallet === SUPPORT_WALLET_ENUM.unisat
            ? "wallet-container"
            : "wallet-container-okx"
        }
        style={{
          width: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <motion.img
          src={selectedLogo}
          alt={selectedWallet}
          layoutId={
            selectedWallet === SUPPORT_WALLET_ENUM.unisat
              ? "unisat-logo"
              : "okx-logo"
          }
        />
      </motion.div>

      {/* 连接状态文本 */}
      {/* 实现一个动画组 1. 连接成功 2. 连接失败 3. 连接中 */}
      <AnimatePresence mode="wait">
        {connectionStage === "initiate" && (
          <motion.p
            key="initiate"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            exit={{ opacity: 0 }}
            style={{
              textAlign: "center",
            }}
          >
            Connection request being initiated...
          </motion.p>
        )}
        {connectionStage === "waiting" && (
          <motion.p
            key="waiting"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            exit={{ opacity: 0 }}
            style={{
              textAlign: "center",
              display: "flex",
              alignItems: "center",
            }}
          >
            Waiting for wallet response <Text>...</Text>
          </motion.p>
        )}
        {connectionStage === "result" && (
          <motion.p
            key="result"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            exit={{ opacity: 0 }}
            style={{
              textAlign: "center",
            }}
          >
            {isSuccess
              ? "Connection successful!"
              : "Connection failed, please close and try again."}
          </motion.p>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

export default memo(SelectWalletStatus);
