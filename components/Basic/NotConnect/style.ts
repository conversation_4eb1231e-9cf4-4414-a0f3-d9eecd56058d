import styled from "styled-components";

export const NotConnectView = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  height: calc(100vh - 220px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 40px;
  &>p{
    font-family: JetBrainsMono;
    font-size: 20px;
    font-weight: 400;
    line-height: 24.2px;
    text-align: center;
    color: #615A57;
  }
  &>button{
    width: 240px;
    height: 56px;
    border-radius: 16px;
    background: #FF8316;
    border: 0;
    outline: none;
    //styleName: button;
    font-family: JetBrainsMonoBold;
    font-size: 18px;
    font-weight: 700;
    line-height: 21.78px;
    color: #FFFFFF;
    cursor: pointer;
  }
`
