import styled from "styled-components";

export const ResourceView = styled.div<{isShow:boolean}>`
  padding-top: 120px;
  position: relative;
  .cards-grid{
    display: grid;
    grid-template-columns: 1fr;
    gap: 1px;
    .grid-items{
      display: grid;
      grid-template-columns: 1fr 2fr 2fr 2fr 2fr 1fr;
      gap: 1px;
      &>div{
        height: 140px;
        border-right: 1px solid #F6AC6D;
        border-bottom: 1px solid #F6AC6D;
        box-sizing: border-box;
        position: relative;
        &:nth-last-child(1){
          border-right: 0;
          border-image: linear-gradient(90deg, #F6AC6D, transparent) 1;
        }
        &:nth-child(1){
          border-image: linear-gradient(270deg, #F6AC6D, transparent) 1;
        }
        &.table-data-item{
          display: flex;
          flex-direction: column;
          justify-content: end;
          padding: 16px;
          box-sizing: border-box;
          transition: all 0.3s linear;
          &>h6{
            font-family: JetBrainsMono;
            font-size: 32px;
            font-weight: 500;
            line-height: 32px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #FFFFFF;
            margin: 0;
            transition: all 0.3s linear;
          }
          &>p{
            font-family: JetBrainsMono;
            font-size: 32px;
            font-weight: 400;
            line-height: 32px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #140F08;
            margin: 0;
            transition: all 0.3s linear;
          }

          &>h6,&>p{
            opacity: 0.6;
          }
        }
        &>.o-top{
          width: 4px;
          height: 4px;
          background: #ffffff;
          position: absolute;
          right: -2px;
          top: -2px;
          box-shadow: 0px 0px 8px 0px #FF8316;

        }
        &>.o-bottom{
          width: 4px;
          height: 4px;
          background: #ffffff;
          position: absolute;
          right: -2px;
          bottom: -2px;
          box-shadow: 0px 0px 8px 0px #FF8316;
        }
        &>.o-l-top{
          width: 4px;
          height: 4px;
          background: #ffffff;
          position: absolute;
          left: -2px;
          top: -2px;
          box-shadow: 0px 0px 8px 0px #FF8316;
        }
        &>.o-l-bottom{
          width: 4px;
          height: 4px;
          background: #ffffff;
          position: absolute;
          left: -2px;
          bottom: -2px;
          box-shadow: 0px 0px 8px 0px #FF8316;
        }
      }

      &:nth-last-child(1){
        &>div{
          border-bottom: 0;
        }
      }
      &:nth-child(1){
        &>div{
          border-image: linear-gradient(360deg, #F6AC6D, transparent) 1;
          &:nth-last-child(1){
            border-right: 0;
            border-image: linear-gradient(90deg, #F6AC6D, transparent) 1;
          }
          &:nth-child(1){
            //border-image: linear-gradient(270deg, #F6AC6D, transparent) 1;
            border-image: linear-gradient(0deg, #F6AC6D, transparent) 1;
          }
        }
      }
      &:nth-last-child(1){
        &>div{
          border-image: linear-gradient(180deg, #F6AC6D, transparent) 1;
        }
      }
    }
  }
  .resource-view{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    padding: 80px 0 0 0;
    .resource-view-box{
      display: flex;
      flex-direction: column;
      justify-content: center;
      max-width: 1472px;
      margin: auto;
      width: 100%;
      &>.title{
        font-family: JetBrainsMono;
        font-size: 40px;
        font-weight: 700;
        line-height: 40px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #140F08;
      }
      .resource-cards{
        margin-top: 140px;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 16px;
        &>.resource-card-item{
          height: 144px;
          border-radius: 24px;
          
          .resource-card-item-box{
            width: 100%;
            height: 100%;
            background: #FFFFFF4D;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 24px;
            backdrop-filter: blur(4px);
            border-radius: 24px;
            &>span{
              font-family: JetBrainsMono;
              font-size: 28px;
              font-weight: 400;
              line-height: 28px;
              text-align: center;
              text-underline-position: from-font;
              text-decoration-skip-ink: none;
              color: #140F08;
            }
            &>img{
              width: 24px;
              height: 24px;
            }

            &>.o-top{
              width: 4px;
              height: 4px;
              background: #140F08;
              position: absolute;
              right: 16px;
              top: 16px;

            }
            &>.o-bottom{
              width: 4px;
              height: 4px;
              background: #140F08;
              position: absolute;
              right: 16px;
              bottom: 16px;
            }
            &>.o-l-top{
              width: 4px;
              height: 4px;
              background: #140F08;
              position: absolute;
              left: 16px;
              top: 16px;
            }
            &>.o-l-bottom{
              width: 4px;
              height: 4px;
              background: #140F08;
              position: absolute;
              left: 16px;
              bottom: 16px;
            }
          }
          &.resource-card-item1{
            transition: all 0.5s linear;
            transform: ${({ isShow }) => isShow ? 'translateY(0)' : 'translateY(100px)'};
            opacity: ${({ isShow }) => isShow ? 1 : 0};
          }
          &.resource-card-item2{
            transform: ${({ isShow }) => isShow ? 'translateY(0)' : 'translateY(200px)'};
            transition: all 0.8s 0.2s linear;
            opacity: ${({ isShow }) => isShow ? 1 : 0};
          }
          &.resource-card-item3{
            transform: ${({ isShow }) => isShow ? 'translateY(0)' : 'translateY(300px)'};
            transition: all 1.1s 0.4s linear;
            opacity: ${({ isShow }) => isShow ? 1 : 0};
          }
        }
      }
    }
    
  }
`
