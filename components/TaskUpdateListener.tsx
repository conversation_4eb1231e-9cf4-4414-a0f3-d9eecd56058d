import React, { useEffect } from 'react';
import { eventBus, EventTypes } from '../utils/eventBus';
import { useTaskContext } from '../contexts/TaskContext';
import { getTaskList } from '../server';

// 这个组件负责监听任务更新事件并同步到TaskContext
// 可以在应用的高层组件（如_app.tsx）中使用
const TaskUpdateListener: React.FC = () => {
  const { syncTaskProgress, incentivesConfig } = useTaskContext();

  useEffect(() => {
    // 监听任务列表更新事件
    const taskUpdateUnsubscribe = eventBus.subscribe(
      EventTypes.TASK_LIST_UPDATED,
      async () => {
        try {
          // 当任务更新事件触发时，获取最新任务列表
          const response = await getTaskList();
          if (response?.data?.data) {
            // console.log('[TaskUpdateListener] 任务列表更新成功');
            syncTaskProgress(response.data.data, incentivesConfig);
          }
        } catch (error) {
          console.error('[TaskUpdateListener] 获取任务列表失败:', error);
        }
      }
    );

    // 直接监听任务数据事件（可选，用于优化性能）
    const taskDataUnsubscribe = eventBus.subscribe(
      EventTypes.TASK_PROGRESS_DATA,
      (taskData) => {
        if (taskData) {
          // console.log('[TaskUpdateListener] 任务数据已更新');
          syncTaskProgress(taskData, incentivesConfig);
        }
      }
    );

    // 组件卸载时取消订阅
    return () => {
      taskUpdateUnsubscribe();
      taskDataUnsubscribe();
    };
  }, [syncTaskProgress, incentivesConfig]);

  // 这个组件不需要渲染任何UI
  return null;
};

export default TaskUpdateListener; 