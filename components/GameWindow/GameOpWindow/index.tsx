import {GameOpWindowView} from "./style";

import {useEffect, useState} from "react";
import GlobalSpace, {GAME_OP_TYPE} from "../../../world/Global/GlobalSpace";
import AxeSvg from '../../../public/image/gameOpIcon/axe.svg'
import ChatSvg from '../../../public/image/gameOpIcon/chat.svg'
import TipSvg from '../../../public/image/gameOpIcon/tip.svg'
import PickaxeSvg from '../../../public/image/gameOpIcon/pickaxe.svg'
import {useSelector} from "react-redux";
import {IAppState} from "@/constant/type";
import {KeyPressUtil} from "@/world/Global/GlobalKeyPressUtil";

interface IProps {
  doorKey?: {
    hasAresKey: boolean,//是否有钥匙
    aresKeyUnlocksTime: number,//钥匙解锁时间
    openCallback: Function,//开门关门的回调
  }
}

export type GameOpItem = {
  type: GAME_OP_TYPE,
  callback: Function,
  sortIndex: number,
  text?: string,
  icon_src?: string
}

export default function GameOpWindow({doorKey}: IProps) {

  const {userBasicInfo} = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const [opCallbackList, setOpCallbackList] = useState<GameOpItem[]>([])
  //获取屏幕大小
  const [width, setWidth] = useState<number>(0)
  const [height, setHeight] = useState<number>(0)
  useEffect(() => {
    setWidth(window.innerWidth)
    setHeight(window.innerHeight)
  }, [])


  useEffect(() => {
    GlobalSpace.watchGameOpChange((opCallbackMap) => {
      const opList: GameOpItem[] = []

      for (const [key, item] of opCallbackMap) {
        opList.push(item)
      }
      opList.sort((a, b) => {
        //取字符串后三位
        return a.sortIndex - b.sortIndex
      })
      setOpCallbackList(opList)
    })
  }, [])

  const onClick = (index: number) => {
    const callback = opCallbackList[index] && opCallbackList[index].callback
    if (callback) {
      callback()
    }
  }
  // 监听按下F键盘
  useEffect(() => {
    const cancel = KeyPressUtil.registerKeyPress(["f", "F", "e", "E", "r", "R"], true, (event) => {
      if (event.key === "F" || event.key === "f") {
        if (opCallbackList.length > 0) {
          onClick(0)
        }
      }
      if (event.key === "E" || event.key === "e") {
        if (opCallbackList.length > 1) {
          onClick(1)
        }
      }
      if (event.key === "R" || event.key === "r") {
        if (opCallbackList.length > 2) {
          onClick(2)
        }
      }
    })
    return () => {
      cancel()
    };
  }, [opCallbackList]);
  return <GameOpWindowView style={{top: height * 0.5 + 50, left: width * 0.5 + 200}}>
    {
      opCallbackList.length > 0 &&
      <div className="currency-box">
        {
          opCallbackList.map((item, index) => (
            <div key={index} className={'currency-box-btns-box'}
                 onClick={() => onClick(index)}>
              <div className="currency-box-btns">
                {
                  index === 0 && <div className="key-f">F</div>
                }
                {
                  index === 1 && <div className="key-f">E</div>
                }
                {
                  index === 2 && <div className="key-f">R</div>
                }
                {
                  index > 2 && <div className="key-f-hide"></div>
                }
                {item.type === GAME_OP_TYPE.PotatoOp && <span>Claim</span>}
                {item.type === GAME_OP_TYPE.ChatOp && <img src={ChatSvg.src} alt="" className="key-icon"/>}
                {item.type === GAME_OP_TYPE.ChatOp && <span>Chat</span>}
                {item.type === GAME_OP_TYPE.HitTree && <img src={AxeSvg.src} alt="" className="key-icon"/>}
                {item.type === GAME_OP_TYPE.HitTree && <span>Cut Tree</span>}
                {item.type === GAME_OP_TYPE.Mining && <img src={PickaxeSvg.src} alt="" className="key-icon"/>}
                {item.type === GAME_OP_TYPE.Mining && <span>Mining</span>}
                {item.type === GAME_OP_TYPE.ShowTip && <img src={TipSvg.src} alt="" className="key-icon"/>}
                {item.type === GAME_OP_TYPE.ShowTip && <span>Event Rules</span>}
                {item.type === GAME_OP_TYPE.CustomOp && <img src={item.icon_src || ''} alt="" className="key-icon"/>}
                {item.type === GAME_OP_TYPE.CustomOp && <span>{item.text || ''}</span>}
              </div>
            </div>
          ))
        }
      </div>
    }
  </GameOpWindowView>
}
