import styled from "styled-components";

export const QuestionHistoryModalView = styled.div`
  border: 4px solid #ff8316;
  background: #fff2e2;
  border-radius: 32px;
  box-sizing: border-box;
  position: relative;
  max-width: 1154px;
  min-height: 300px;
  width: 100%;
  padding: 60px 40px 40px;

  .close-btn {
    position: absolute;
    /* width: 56px; */
    /* height: 56px; */
    cursor: pointer;
    top: -18px;
    right: 40px;
  }

  .history-title {
    position: absolute;
    left: 24px;
    top: -42px;
    /* width: 358px; */
    /* height: 84px; */
  }

  .question-history-box {
    /* display: flex;
    gap: 24px;
    height: 600px; */

    /* .history-list {
      width: 360px;
      overflow-y: auto;
      background: #F7E7CD;
      border-radius: 24px;
      padding: 8px;
      display: flex;
      flex-direction: column;
      gap: 8px;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #C69F7E;
        border-radius: 3px;
      }

      .history-item {
       background: #FBF4E8;
        box-shadow: 4px 4px 4px 0px #FFFFFF inset,0px 2px 8px 0px #00000026;
        padding: 8px 16px;
        box-sizing: border-box;
        font-family: Inter;
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        color: #140F08;
        overflow:hidden;
        border-radius: 12px;
        cursor: pointer;
        &>span{

          overflow:hidden;
          text-overflow:ellipsis;
          display:-webkit-box;
          -webkit-box-orient:vertical;
          -webkit-line-clamp:2;
          border-radius: 12px;
        }

        &.active {
          background: #ECDBC6;
          border: 1px solid #A58061;
          box-sizing: border-box;
          box-shadow: none;
        }
      }
    } */

    /* .history-content {
      flex: 1;
      box-shadow: 4px 4px 4px 0px #ffffff inset, 0px 2px 8px 0px #00000026;
      background: #fbf4e8;
      padding: 32px 40px 24px 54px;
      box-sizing: border-box;
      border-radius: 20px;
      .content-item {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        .question {
          max-width: 452px;
          background: #f7e7cd;
          border: 1px solid #a58061;
          font-family: Inter;
          font-weight: 400;
          font-size: 16px;
          line-height: 20px;
          padding: 8px 16px;
          color: #140f08;
          align-self: end;
          border-radius: 12px;
        }

        .answer {
          flex: 1;
          overflow: hidden;
          margin-top: 32px;
          .answer-box {
            width: 100%;
            height: 100%;
            overflow-y: auto;

            font-family: Inter;
            font-weight: 400;
            font-size: 16px;
            line-height: 20px;
            color: #140f08;
            &::-webkit-scrollbar {
              width: 6px;
            }

            &::-webkit-scrollbar-thumb {
              background: #c69f7e;
              border-radius: 3px;
            }
          }
        }

        .files {
          margin-top: 16px;
          padding-top: 16px;
          border-top: 1px solid #cabfab;
          display: flex;
          flex-wrap: wrap;
          column-gap: 16px;
          row-gap: 8px;

          span {
            font-family: Inter;
            font-weight: 400;
            font-size: 16px;
            line-height: 32px;
            color: #686663;
          }

          a {
            background: #ecdbc6;
            border-radius: 8px;
            cursor: pointer;
            padding: 2px 8px;
            box-sizing: border-box;
            font-family: Inter;
            font-weight: 400;
            font-size: 14px;
            line-height: 28px;
            color: #686663;
            text-decoration: none;
            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    } */
  }
`;

export const QuestionHistoryBox = styled.div`
  display: flex;
  gap: 24px;
  height: 600px;
`;

export const HistoryListEmpty = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  min-width: 360px;
  background: #f7e7cd;
  border-radius: 24px;
  padding: 8px;
`;

export const HistoryListWrapper = styled.div`
  // 历史列表的样式
  width: 360px;
  overflow-y: auto;
  background: #f7e7cd;
  border-radius: 24px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c69f7e;
    border-radius: 3px;
  }

  .scrollable {
    overflow-y: auto;
    /* max-height: 70vh; */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c69f7e;
      border-radius: 3px;
    }
  }

  .history-item {
    background: #fbf4e8;
    box-shadow: 4px 4px 4px 0px #ffffff inset, 0px 2px 8px 0px #00000026;
    padding: 8px 16px;
    box-sizing: border-box;
    font-family: Inter;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    color: #140f08;
    overflow: hidden;
    border-radius: 12px;
    cursor: pointer;
    margin: 8px 0;
    & > span {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      border-radius: 12px;
    }

    &.active {
      background: #ecdbc6;
      border: 1px solid #a58061;
      box-sizing: border-box;
      box-shadow: none;
    }
  }
`;

export const HistoryContentWrapper = styled.div`
  // 内容区域的样式
  flex: 1;
  box-shadow: 4px 4px 4px 0px #ffffff inset, 0px 2px 8px 0px #00000026;
  background: #fbf4e8;
  padding: 32px 40px 24px 54px;
  box-sizing: border-box;
  border-radius: 20px;
`;

export const HistoryContentItem = styled.div`
  display: flex;
  flex-direction: column;
  /* width: 100%; */
  height: 100%;
  width: 600px;
`;

export const HistoryContentItemNoSelection = styled.div`
  /* width: 100%; */
  height: 100%;
  width: 600px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  .no-selection-text {
    font-size: 14px;
  }
  .empty-state {
    width: 64px;
    height: 64px;
    background-image: url("/image/emp.svg");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
`;

export const QuestionSectionWrapper = styled.div`
  // 问题部分的样式
  max-width: 452px;
  background: #f7e7cd;
  border: 1px solid #a58061;
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 20px;
  padding: 8px 16px;
  color: #140f08;
  align-self: end;
  border-radius: 12px;
`;

export const AnswerSectionWrapper = styled.div`
  // 回答部分的样式
  flex: 1;
  overflow: hidden;
  margin-top: 32px;
  .answer-box {
    width: 100%;
    height: 100%;
    overflow-y: auto;

    font-family: Inter;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    color: #140f08;
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c69f7e;
      border-radius: 3px;
    }
  }
`;

export const CitationSectionWrapper = styled.div`
  // 引用部分的样式
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #cabfab;
  display: flex;
  flex-wrap: wrap;
  column-gap: 16px;
  row-gap: 8px;

  span {
    font-family: Inter;
    font-weight: 400;
    font-size: 16px;
    line-height: 32px;
    color: #686663;
  }

  a {
    background: #ecdbc6;
    border-radius: 8px;
    cursor: pointer;
    padding: 2px 8px;
    box-sizing: border-box;
    font-family: Inter;
    font-weight: 400;
    font-size: 14px;
    line-height: 28px;
    color: #686663;
    text-decoration: none;
    &:hover {
      text-decoration: underline;
    }
  }
`;
