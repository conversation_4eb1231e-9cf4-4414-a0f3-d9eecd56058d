import AnswerSection from "./AnswerSection";
import CitationSection from "./CitationSection";
import { HistoryContentItem, HistoryContentItemNoSelection } from "../style";
import QuestionSection from "./QuestionSection";
import { IAssistantQuestionHistory } from "@/constant/type";
import { memo } from "react";
import { ConnectWalletBtn } from "@/components/Header";
import { motion, AnimatePresence } from "motion/react";
import LocalLoading from "@/components/LoadingContent";

interface HistoryContentProps {
  history: IAssistantQuestionHistory | null;
  address: string;
  historyList?: IAssistantQuestionHistory[];
  isLoading?: boolean;
}

// 未连接钱包
function NotConnectedContent() {
  return (
    <HistoryContentItemNoSelection>
      <ConnectWalletBtn />
      <div className="no-selection-text">Please connect your wallet</div>
    </HistoryContentItemNoSelection>
  );
}

// 没有历史记录
function EmptyHistoryContent() {
  return (
    <HistoryContentItemNoSelection>
      <div className="empty-state" />
      <div className="no-selection-text">
        There is currently no data available
      </div>
    </HistoryContentItemNoSelection>
  );
}

function NoSelectionContent() {
  return (
    <HistoryContentItemNoSelection>
      <div className="no-selection-text">Select a conversation</div>
      <div className="no-selection-subtext">
        There is currently no data available
      </div>
    </HistoryContentItemNoSelection>
  );
}

// 添加Loading组件
function LoadingContent() {
  return (
    <HistoryContentItemNoSelection>
      <LocalLoading />
    </HistoryContentItemNoSelection>
  );
}

const HistoryContent: React.FC<HistoryContentProps> = ({
  history,
  address,
  historyList = [],
  isLoading,
}) => {
  // 首先检查是否正在加载
  if (isLoading && address) {
    return <LoadingContent />;
  }

  if (!address) {
    return <NotConnectedContent />;
  }

  if (historyList.length === 0) {
    return <EmptyHistoryContent />;
  }

  if (!history) {
    return <NoSelectionContent />;
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={history._id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, ease: "easeInOut" }}
        style={{ width: "100%", height: "100%" }}
        exit={{ opacity: 0, y: -20 }}
      >
        <HistoryContentItem>
          <QuestionSection prompt={decodeURIComponent(history.prompt) || ""} />
          <AnswerSection content={history.content || ""} />
          <CitationSection files={history.files || []} />
        </HistoryContentItem>
      </motion.div>
    </AnimatePresence>
  );
};

export default memo(HistoryContent);
