import styled from "styled-components";

export const RecordingBoxView = styled.div<{ isHidden: boolean, width: string }>`
  position: fixed;
  right: ${({ isHidden }) => isHidden ? '0' : '64px'};
  top: 50%;
  transform: translate(${({ isHidden }) => isHidden ? '100%' : '0'}, -50%);
  width: ${({ width }) => width || '488px'};
  max-width: calc(95vw);
  z-index: 2;
  box-sizing: border-box;
  border-radius: 32px;
  transition: all 0.3s linear;

  .recording-box-menu {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translate(-100%, -50%);
    padding-right: ${({ isHidden }) => isHidden ? '64px' : '10px'};
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .recording-box-menu-item {
      width: 72px;
      height: 72px;
      box-sizing: border-box;
      border: 3.27px solid #945F00;
      border-radius: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(270deg, #FF8316 49.72%, #9D4A00 312.5%);
      cursor: pointer;

      & > img {
        width: 32px;
        height: 32px;
      }

      &.active {
        background: #FFF2E2;
        border-color: #ED9800;
      }
    }
    .hidden-btn {
      transform: rotate(${({ isHidden }) => isHidden ? '90deg' : '270deg'});
      cursor: pointer;
    }
  }
`

export const RecordingView = styled.div`
  & > .recording-box {
    width: 100%;
    height: 100%;
    border: 8px solid #FF8316;
    border-radius: 32px;
    background: #FFF2E2;
    padding: 28px 36px;
    box-sizing: border-box;
    box-shadow: 2px 2px 4px 0px #FFFFFF99 inset, 0px 4px 4px 0px #00000040;

    & > textarea {
      border: 1px solid #9A9A9A;
      border-radius: 24px;
      background: #ffffff;
      width: 100%;
      height: 128px;
      padding: 16px;
      box-sizing: border-box;
      font-family: JetBrains Mono;
      font-size: 18px;
      font-weight: 400;
      line-height: 23.76px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #140F08;
      outline: none;

      &::-webkit-resizer {
        display: none;
      }

      &::placeholder {
        font-family: JetBrains Mono;
        font-size: 18px;
        font-weight: 400;
        line-height: 23.76px;
        color: #686663;
      }
    }

    & > .audio-view {
      margin-top: 24px;
      display: flex;
      align-items: center;
      gap: 8px;

      & > span {
        width: 64px;
        text-align: center;
        font-family: JetBrains Mono;
        font-size: 12px;
        font-weight: 400;
        line-height: 15.84px;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #686663;
      }

      & > div {
        flex: 1;
        height: 1px;
        background: #FFFFFF;
        border-radius: 50%;
        overflow: hidden;

        & > div {
          height: 100%;
          border-radius: 50%;
          background: #FF8316;
        }
      }
    }

    & > .actions {
      margin-top: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 24px;

      button {
        cursor: pointer;
        background: #FF8316;
        box-shadow: 0px -4px 0px 0px #00000040 inset;
        height: 64px;
        border-radius: 16px;
        border: 0;

        & > img {
          width: 32px;
          height: 32px;

          &.loading-ano {
            animation: loading-ano 1s infinite;
            @keyframes loading-ano {
              0% {
                transform: rotate(0deg);
              }
              100% {
                transform: rotate(360deg);
              }
            }
          }
        }

        &[disabled] {
          cursor: not-allowed;
          background: #C9B7A5;
          box-shadow: none;
        }

        &.get-audio {
          width: 240px;
        }

        &.next, &.audio-play {
          width: 136px;
        }

        &.prev {
          width: 80px;

          & > img {
            transform: rotateZ(180deg);
          }
        }

        &.recording {
          width: 240px;
        }
      }
    }
  }
`

export const HistoryListView = styled.div`
  width: 100%;
  height: 746px;
  max-height: calc(100vh - 200px);
  box-sizing: border-box;
  background: #FFF2E2;
  border: 4px solid #ED9800;
  border-radius: 32px;
  padding: 24px 10px 24px 24px;
  & > div {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    box-sizing: border-box;
    padding-right: 14px;
    display: flex;
    &>div{
      display: flex;
      flex-direction: column;
      gap: 24px;
      width: 100%;
      
    }
    .empty-history{
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #615A57;
    }
    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 12px;
      height: 12px;
      background: #00000010;
      border-radius: 8px;
    }

    &::-webkit-scrollbar-thumb {
      background: #000;
      border-radius: 12px;
    }
  }
`

export const HistoryItemView = styled.div<{ bg: string,active:boolean }>`
  width: 100%;

  .poster-img {
    width: 100%;
    height: 144px;
    background-image: url("${({bg}) => bg}");
    background-size: cover;
    background-position: center center;
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    border: ${({ active }) => active ? '4px solid #ED9800' : null};
    box-sizing: border-box;

    & > .poster-img-body {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #00000080;
      backdrop-filter: blur(8px);
      border-radius: 20px;

      & > img {
        border-radius: 20px;
        max-width: 100%;
        max-height: 100%;
      }
    }

    & > .poster-img-action {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 2;
      background-color: #00000080;
      display: none;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(8px);
      &.loading{
        display: flex;
      }

      & > .play-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 64px;
        height: 64px;
        background-color: #FF8316;
        box-shadow: 0px -4px 0px 0px #00000040 inset, 0px 4px 4px 0px #00000040;
        border-radius: 50%;
        cursor: pointer;
        &.play-btn-view{
          width: 136px;
          border-radius: 50px;
        }

        & > img {
          width: 32px;
          height: 32px;
        }
        &>span{
          font-family: JetBrains Mono;
          font-size: 20px;
          font-weight: 700;
          line-height: 26.4px;
          letter-spacing: -0.04em;
          text-align: center;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #FFFFFF;
        }
      }

      & > .loading-img {
        width: 40px;
        height: 40px;
        animation: loading-ano 1s infinite;
        @keyframes loading-ano {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      }
    }

    &:hover {
      & > .poster-img-action {
        display: ${({ active }) => active ? 'none' : 'flex!important'};
      }
    }
  }

  & > .desc {
    font-family: JetBrains Mono;
    font-size: 14px;
    font-weight: 400;
    line-height: 16px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: ${({ active }) => active ? '#ED9800' : '#140F08'};
    margin: 4px 0 0 0;
    // 2行之后省略号
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;

  }

  & > .time {
    font-family: JetBrains Mono;
    font-size: 14px;
    font-weight: 400;
    line-height: 18.48px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #615A57;
    margin: 2px 0 0 0;
  }

`
