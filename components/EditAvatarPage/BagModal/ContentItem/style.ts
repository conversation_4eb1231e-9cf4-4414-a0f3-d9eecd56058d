import styled from "styled-components";
import KeyBg from "/public/image/bag/key-bg.png";
import effectPng from "/public/image/effect.png";

export const ContentItemView = styled.div<{
  check?: boolean;
  disabled?: boolean;
  boxShadow?: string;
  effect?: boolean;
  isGlowWeapon?: boolean;
  currentDurability?: number;
}>`
  width: 120px;
  height: 120px;
  box-shadow: ${({ boxShadow }) => boxShadow || "0px 2px 8px 0px #00000026"};
  border: ${({ check }) => (check ? "4px solid #FF8316" : "1px solid #CABFAB")};
  background: #fbf4e8;
  border-radius: 20px;
  position: relative;
  cursor: ${({ disabled }) => (disabled ? "not-allowed" : "pointer")};
  filter: ${({ disabled }) => (disabled ? "grayscale(100%)" : "none")};
  box-sizing: border-box;
  /* effect如果为true，则使用伪类元素加载图片 */
  &::before {
    content: ${({ effect, isGlowWeapon }) =>
      effect || isGlowWeapon ? '""' : "none"};
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url(${effectPng.src});
    background-size: 100% 100%;
    display: ${({ effect, isGlowWeapon }) =>
      effect || isGlowWeapon ? "block" : "none"};
    z-index: 6;
  }
  // currentDurability 为 1时才显示
  &::after {
    content: ${({ currentDurability }) =>
      currentDurability === 1 ? '""' : "none"};
    position: absolute;
    top: 0%;
    left: 0%;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    background: radial-gradient(
      circle at center,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.9) 45%,
      rgba(255, 182, 193, 0.7) 50%,
      rgba(255, 105, 180, 0.4) 75%,
      rgba(255, 20, 147, 0.2) 90%,
      rgba(255, 105, 180, 0) 100%
    );
    display: ${({ currentDurability }) =>
      currentDurability === 1 ? "block" : "none"};
    z-index: 5;
    filter: blur(7px);
    opacity: 1;
  }

  & > .content-image {
    width: 100%;
    height: 100%;
    border-radius: 20px;
    position: absolute;
    z-index: 7;
    left: 0;
    top: 0;
  }
  & > .content-num {
    position: absolute;
    left: 20px;
    bottom: 5px;
    font-family: JetBrainsMono;
    font-size: 14px;
    font-weight: 400;
    line-height: 14px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    z-index: 9;
    color: #ffffff;
    // 文字描边
    text-shadow: 1px 1px 0 #a58061, -1px 1px 0 #a58061, 1px -1px 0 #a58061,
      -1px -1px 0 #a58061;
  }
  & > .content-new {
    width: 48px;
    position: absolute;
    right: -8px;
    top: -10px;
  }
  & > .quest-key {
    width: 56px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url("${KeyBg.src}");
    background-size: 100% 100%;
    z-index: 10;
    font-family: JetBrainsMono;
    font-size: 20px;
    font-weight: 400;
    line-height: 20px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #ffffff;
    position: absolute;
    right: -12px;
    top: -6px;
    padding-bottom: 10px;
    box-sizing: border-box;
  }
  & > .red-mark {
    width: 16px;
    height: 16px;
    background: #ff4516;
    border: 2px solid #140f08;
    border-radius: 50%;
    box-sizing: border-box;
    position: absolute;
    left: -2px;
    top: -2px;
    box-shadow: 0px 4px 4px 0px #00000040;
  }
`;

export const TooltipBoxView = styled.div<{
  quality?: number;
}>`
  width: 430px;
  height: auto;
  background: #ffffff;
  border: 4px solid #140f08;
  box-shadow: 0px 4px 16px 0px #00000040;
  padding: 24px;
  box-sizing: border-box;
  border-radius: 32px;
  position: relative;

  .tooltip-box-title {
    color: ${({ quality }) => {
      switch (quality) {
        case 1:
          return "#140F08";
        case 2:
          return "#00C724";
        case 3:
          return "#00BBFF";
        case 4:
          return "#9279FF";
        case 5:
          return "#FFD900";
        case 6:
          return "#FF9500";
        case 7:
          return "#FF2727";
        default:
          return "#140F08";
      }
    }};
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 28px;
    border: 2px solid
      ${({ quality }) => {
        switch (quality) {
          case 1:
            return "#fff";
          case 2:
            return "#00C724";
          case 3:
            return "#00BBFF";
          case 4:
            return "#9279FF";
          case 5:
            return "#FFD900";
          case 6:
            return "#FF9500";
          case 7:
            return "#FF2727";
          default:
            return "#fff";
        }
      }};
    pointer-events: none;
  }

  & > h3 {
    margin: 0;
    font-family: JetBrainsMono;
    font-size: 24px;
    font-weight: 700;
    line-height: 31.68px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #140f08;
  }
  & > div {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 24px;
    & > p {
      font-family: JetBrainsMono;
      font-size: 20px;
      font-weight: 400;
      line-height: 26.4px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #686663;
      margin: 0;
      display: flex;
      & > span {
        flex: 1;
        color: #3f3b37;
      }
    }
  }
`;
