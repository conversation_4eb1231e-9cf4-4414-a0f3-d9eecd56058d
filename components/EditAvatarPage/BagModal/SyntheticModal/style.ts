import styled from "styled-components";

export const SyntheticModalView = styled.div`
  width: 760px;
  background: #FFF2E2;
  box-shadow: 2px 2px 4px 0px #FFFFFF99 inset;
  border-radius: 48px;
  border: 8px solid #ED9800;
  box-sizing: border-box;
  position: relative;
  &>.synthetic-title{
    width: 358px;
    height: 84px;
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%, -50%);
  }
  &>.close-btn{
    width: 56px;
    height: 56px;
    position: absolute;
    right: 40px;
    top: -22px;
    cursor: pointer;
  }
  &>.synthetic-content{
    padding: 78px 30px 50px 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    max-height: calc(100vh - 80px);
    overflow-y: auto;
    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      border-radius: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: #595754;
      width: 8px;
      height: 8px;
      border-radius: 8px;
    }
    // 右下角
    &::-webkit-scrollbar-corner {
      background: transparent;
    }
    &>.result-item{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      &>p{
        margin: 8px 0 0 0;
        font-family: JetBrainsMono;
        font-size: 20px;
        font-weight: 700;
        line-height: 20px;
        text-align: center;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #140F08;
      }
    }
    &>.depletion-list{
      margin-top: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #F7E7CD;
      padding: 16px;
      box-sizing: border-box;
      max-width: 488px;
      gap: 48px;
      border-radius: 32px;
      box-shadow: 0px 0px 8px 0px #00000040 inset;
      &>.depletion-item{
        &>p{
          margin: 16px 0 0 0;
          font-family: JetBrainsMono;
          font-size: 20px;
          font-weight: 400;
          line-height: 20px;
          text-align: center;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #615A57;
          &.not-enough{
            color: #FF8316;
          }
        }
      }
    }
    &>.num-view{
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 16px;
    }
    &>.confirm-btn{
      margin: 32px auto 0 auto;
      width: 240px;
      min-height: 64px;
      background: linear-gradient(180deg, #FFBD2E 0%, #FF8316 48%, #FF902F 100%),
      linear-gradient(0deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.5));
      box-shadow: 0px 2px 2px 0px #FFCE47 inset;
      border-radius: 24px;
      border: 0;
      cursor: pointer;
      outline: none;
      &>span{
        font-family: JetBrainsMono;
        font-size: 20px;
        font-weight: 700;
        line-height: 26.4px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #FFFFFF;
        text-shadow:
          2px 2px 0 #6F3400,
          -2px 2px 0 #6F3400,
            2px -2px 0 #6F3400,
            -2px -2px 0 #6F3400;
      }
      &[disabled]{
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
`

export const NumStepView = styled.div`
  width: 216px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #F7E7CD;
  padding: 8px;
  box-sizing: border-box;
  &>div{
    width: 40px;
    height: 40px;
    border-radius: 12px;
    background: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: JetBrainsMono;
    font-size: 20px;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #140F08;
    cursor: pointer;
    &.disabled{
      cursor: not-allowed;
      background: #D8D0CA;
      color: #5F5D5B;
    }
  }
  &>span{
    flex: 1;
    font-family: JetBrainsMono;
    font-size: 20px;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #140F08;
  }
`
