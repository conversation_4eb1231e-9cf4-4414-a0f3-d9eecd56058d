import styled from "styled-components";
import { THEME_MEDIA_ENUM } from "../../../AvatarOrdinalsBrowser/constant";

export const StorageMenuView = styled.div`
  display: none;
  ${THEME_MEDIA_ENUM.FRONTEND_LARGE} {
    display: flex !important;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 32px;
    position: fixed;
    left: 32px;
    top: 50%;
    transform: translate(0, -50%);
    .storage-menu-item {
      width: 72px;
      height: 72px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 16px;
      cursor: pointer;
      border-bottom: 6px solid #b44711;
      background: linear-gradient(
        to bottom,
        #fc9849 1%,
        #ff8316 2%,
        #fe6a00 97%,
        #fb5e0e 100%
      );
      border-top: none;
      box-shadow: 0px 0px 8px 0px #00000040 inset,
        0px 4px 8px 0px rgba(0, 0, 0, 0.3);

      &:hover:not(.active) {
        background: linear-gradient(
          to bottom,
          #fdbb4b 1%,
          #ffae16 2%,
          #fe9400 97%,
          #fb8f1e 100%
        );
        border-bottom: 6px solid #b46615;
        border-top: none;
        box-shadow: 0px 0px 8px 0px #00000040 inset,
          0px 4px 8px 0px rgba(0, 0, 0, 0.3);
      }
      &.active {
        background: #292116;
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.3);
        border-top: 6px solid rgba(0, 0, 0, 0.5);
        border-bottom: none;
      }
      .edit-paths-icon {
        width: 52px;
        height: 48px;
      }
      .edit-scene-icon {
        width: 48px;
        height: 48px;
      }
      .icon-container {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      /* .backpack-icon {
        &::after {
          content: "B";
          position: absolute;
          top: 50%;
          right: 20%;
          width: 40px;
          height: 40px;
          background: #000;
          border-radius: 10px;
          color: #fff;
          font-weight: 900;
          font-size: 24px;
          display: flex;
          justify-content: center;
          align-items: center;
          transform: translate(100%, -50%);
          opacity: 0;
          transition: opacity 0.2s ease;
        }
      } */

      .default-icon {
        position: absolute;
        opacity: 1;
        transition: opacity 0.2s ease;
      }

      /* .hover-icon {
        position: absolute;
        opacity: 0;
        transition: opacity 0.2s ease;
      } */

      /* &:hover:not(.active) .default-icon {
        opacity: 0;
      } */

      /* &:hover:not(.active) .hover-icon {
        opacity: 1;
      } */
    }
  }
`;
