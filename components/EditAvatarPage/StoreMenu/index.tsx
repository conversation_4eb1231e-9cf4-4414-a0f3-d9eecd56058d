import { StorageMenuView } from "./style";
import { useEffect, useState } from "react";
import { IAppState, STORAGE_MENU_ENUM } from "../../../constant/type";
import PathMenuSvg from "/public/image/storage-menu/path-menu.svg";
import NftMenuSvg from "/public/image/storage-menu/nft-menu.svg";
import PlayMenuSvg from "/public/image/storage-menu/play-menu.svg";
import { useDispatch, useSelector } from "react-redux";
import { setMenuVisible, setStorageMenu } from "../../../store/app";
import { IBasicSummaryData, SCENE_TYPE } from "../../../constant/type";
import GlobalSpaceEvent, {
  GlobalDataKey,
  SpaceStatus,
} from "../../../world/Global/GlobalSpaceEvent";
import RecordingModal from "../RecordingModal";
import CharacterSelectionModal from "../RecordingModal/CharacterSelectionModal";
import { useRouter } from "next/router";
import BagModal from "../BagModal";
import RecordButton from "./components/RecordButton";
import BackpackButton from "./components/BackpackButton";
import TaskButton from "./components/TaskButton";
// 预加载所有图片
function preloadImages(images: string[]) {
  images.forEach((src) => {
    const img = new Image();
    img.src = src;
  });
}

export default function StorageMenu({
  basicSummaryData,
}: {
  basicSummaryData: IBasicSummaryData | null;
}) {
  const router = useRouter();
  const dispatch = useDispatch();

  const { sceneType, storageMenu } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );

  // 预加载所有图片
  useEffect(() => {
    preloadImages([PathMenuSvg.src, NftMenuSvg.src, PlayMenuSvg.src]);
  }, []);

  useEffect(() => {
    switch (storageMenu) {
      case STORAGE_MENU_ENUM.PATH_MENU:
        GlobalSpaceEvent.SetDataValue<SpaceStatus>(
          GlobalDataKey.SpaceStatus,
          SpaceStatus.Avatar
        );
        break;
      case STORAGE_MENU_ENUM.NFT_MENU:
        GlobalSpaceEvent.SetDataValue<SpaceStatus>(
          GlobalDataKey.SpaceStatus,
          SpaceStatus.NFT
        );
        dispatch(setMenuVisible(false));
        break;
      case STORAGE_MENU_ENUM.PLAY_MENU:
        GlobalSpaceEvent.SetDataValue<SpaceStatus>(
          GlobalDataKey.SpaceStatus,
          SpaceStatus.Game
        );
        dispatch(setMenuVisible(false));
        break;
      default:
        break;
    }
  }, [storageMenu]);


  return (
    <>
      {sceneType === SCENE_TYPE.Room && router.pathname !== "/avatar" && (
        <RoomMenu basicSummaryData={basicSummaryData} />
      )}
      <div
        style={{
          display:
            sceneType === SCENE_TYPE.Island ||
            sceneType === SCENE_TYPE.Community
              ? "block"
              : "none",
        }}
      >
        <LandMenu basicSummaryData={basicSummaryData} />
      </div>
    </>
  );
}

// 房间菜单
function RoomMenu({
  basicSummaryData,
}: {
  basicSummaryData: IBasicSummaryData | null;
}) {
  const dispatch = useDispatch();
  const { storageMenu, isTtsWhiteList } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const [isShowSelectCharacter, setIsShowSelectCharacter] =
    useState<boolean>(false);
  const isDisabledPet = basicSummaryData
    ? !basicSummaryData.petInfo.claim
    : true;

  return (
    <>
      <StorageMenuView>
        {/* 装饰按钮 */}
        <div
          className={
            storageMenu === STORAGE_MENU_ENUM.PATH_MENU
              ? "storage-menu-item active"
              : "storage-menu-item"
          }
          onClick={() => {
            dispatch(setStorageMenu(STORAGE_MENU_ENUM.PATH_MENU));
            dispatch(setMenuVisible(true));
          }}
        >
          <div className="icon-container">
            <img
              src={PathMenuSvg.src}
              className="default-icon"
              alt="Path Menu"
            />
          </div>
        </div>
        {/* NFT 按钮 */}
        <div
          className={
            storageMenu === STORAGE_MENU_ENUM.NFT_MENU
              ? "storage-menu-item active"
              : "storage-menu-item"
          }
          onClick={() => {
            dispatch(setStorageMenu(STORAGE_MENU_ENUM.NFT_MENU));
            dispatch(setMenuVisible(false));
          }}
        >
          <div className="icon-container">
            <img src={NftMenuSvg.src} className="default-icon" alt="NFT Menu" />
          </div>
        </div>
        {/* 游戏按钮 */}
        <div
          className={
            storageMenu === STORAGE_MENU_ENUM.PLAY_MENU
              ? "storage-menu-item active"
              : "storage-menu-item"
          }
          onClick={() => {
            if (isDisabledPet || !isTtsWhiteList) {
              dispatch(setStorageMenu(STORAGE_MENU_ENUM.PLAY_MENU));
            } else {
              setIsShowSelectCharacter(true);
            }
            dispatch(setMenuVisible(false));
          }}
        >
          <div className="icon-container">
            <img
              src={PlayMenuSvg.src}
              className="default-icon"
              alt="Play Menu"
            />
          </div>
        </div>
      </StorageMenuView>
      <CharacterSelectionModal
        visible={isShowSelectCharacter}
        onClose={() => setIsShowSelectCharacter(false)}
        basicSummaryData={basicSummaryData}
      />
    </>
  );
}

// 岛屿菜单
function LandMenu({
  basicSummaryData,
}: {
  basicSummaryData: IBasicSummaryData | null;
}) {
  const { isRecording } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );

  return (
    <>
      {!isRecording && (
        <StorageMenuView style={{ zIndex: 10 }}>
          {/* 场景切换按钮 */}
          <RecordButton />
          {/* 任务按钮 */}
          <TaskButton />
          {/* 背包按钮 */}
          <BackpackButton />
        </StorageMenuView>
      )}
      {/* 背包弹窗 */}
      <BagModal />
      {/* 场景弹窗 */}
      <RecordingModal basicSummaryData={basicSummaryData} />
    </>
  );
}
