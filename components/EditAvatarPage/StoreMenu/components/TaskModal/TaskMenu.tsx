import styled from "styled-components";
import menuImg from "/public/image/task/menu.png";
import Image from "next/image";
import ActiveMenu from "/public/image/task/active-menu.png";
import { useTaskContext } from "contexts/TaskContext";
import newImg from "/public/image/task/new.png";
import { useEffect } from "react";

const TaskMenuContainer = styled.div`
  flex: 17%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-height: 340px;
  .menu-item {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
    z-index: 10;
    position: relative;
    span {
      font-size: 12px;
      font-weight: 600;
      color: #fff;
    }
    .menu-item-icon {
      position: relative;
      .new-badge {
        position: absolute;
        top: -20px;
        left: -30px;
      }
    }
  }
`;

const MenuItem = styled.div`
  position: absolute;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 70%;
  height: 100%;
  background-image: url(${menuImg.src});
  z-index: -1;
`;

const ActiveMenuItem = styled.div<{ top: number }>`
  position: absolute;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 160px;
  height: 90px;
  background-image: url(${ActiveMenu.src});
  z-index: 0;
  top: ${(props) => props.top}px;
  transition: top 0.3s ease;
`;



interface TaskMenuProps {
  activeMenuType?: number;
  onMenuChange?: (type: number) => void;
  menus: {
    name: string;
    type: number;
    icon: string;
    taskType: string;
  }[];
}

const TaskMenu = ({ activeMenuType = 1, onMenuChange, menus }: TaskMenuProps) => {
  const { hasNewInTaskType, clearNewStatusForTaskType, setLastViewedTaskType } = useTaskContext();


  // 移除本地状态，使用传入的activeMenuType
  const handleMenuClick = (type: number, taskType: string) => {
    clearNewStatusForTaskType(taskType);
    // 调用传入的回调函数
    onMenuChange?.(type);
  };

  // 计算ActiveMenu的top位置
  const getActiveMenuTop = () => {
    const index = menus.findIndex((menu) => menu.type === activeMenuType);
    // 根据索引计算每个菜单项的位置
    // 考虑到TaskMenuContainer的布局和三个菜单项的等分
    if (index === 0) return 16; // 第一个菜单项位置
    if (index === 1) return 130; // 第二个菜单项位置
    if (index === 2) return 242; // 第三个菜单项位置
    return 10; // 默认返回第一个位置
  };

  useEffect(() => {
    const menu = menus.find((menu) => menu.type === activeMenuType);
    if (menu) {
      setLastViewedTaskType(menu.taskType);
    }
  }, []);

  return (
    <TaskMenuContainer>
      <MenuItem />
      <ActiveMenuItem top={getActiveMenuTop()} />
      {menus.map((menu) => (
        <div
          key={menu.type}
          className="menu-item"
          onClick={() => handleMenuClick(menu.type, menu.taskType)}
        >
          <div className="menu-item-icon">
            <Image src={menu.icon} width={30} height={30} alt={menu.name} />
            {hasNewInTaskType(menu.taskType) && (
              <div className="new-badge">
                <Image src={newImg.src} alt="new" width={44} height={44} />
              </div>
            )}
          </div>
          <span>{menu.name}</span>
        </div>
      ))}
    </TaskMenuContainer>
  );
};

export default TaskMenu;
