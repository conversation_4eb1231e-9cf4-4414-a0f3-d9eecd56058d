import { motion } from "motion/react";
import styled from "styled-components";

export const ModalContent = styled(motion.div)`
  /* overflow-y: auto; */
  transform-origin: center bottom; /* 设置变换原点为底部中心，更符合弹跳效果 */
  border: 2px solid #ff8316;
  background: #fff2e2;
  border-radius: 20px;
  box-sizing: border-box;
  position: relative;
  max-width: 920px;
  min-height: 500px;
  width: 100%;
  padding: 10px;
  z-index: 100;
  .close-btn {
    position: absolute;
    cursor: pointer;
    top: -18px;
    right: 40px;
  }

  .history-title {
    position: absolute;
    left: 18%;
    top: -9%;
    transform: translate(-50%, 10%);
  }
  .task-content {
    width: 100%;
    height: 470px;
    display: flex;
    overflow-y: auto;
    box-sizing: border-box;
    padding-top: 30px;
  }
`;
export const ModalWrapper = styled.div`
  position: relative;
  height: 100%;
`;
