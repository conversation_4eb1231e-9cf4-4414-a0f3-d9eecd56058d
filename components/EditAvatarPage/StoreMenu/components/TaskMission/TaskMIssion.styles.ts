import styled from "styled-components";

export const TaskMissionContainer = styled.div`
  // 定位在左上角
  position: fixed;
  top: 15%;
  left: 30px;
  z-index: 100;
  // 边框为白色
  display: flex;
  flex-direction: column;
  height: 32px;
  box-sizing: border-box;

  .title {
    display: flex;
    /* align-items: center; */
    gap: 10px;
    background: #140f08;
    color: #fff;
    border-radius: 50px;
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    min-width: 300px;

    .icon {
      width: 36px;
      height: 40px;
      position: absolute;
      left: -2px;
      top: -12px;
    }
    .text {
      text-shadow: 0 0 black;
      font-weight: 900;
      margin: 0;
      flex: 1;
      padding-left: 40px;
    }
  }

  // 所有任务完成的样式
  .all-completed {
    display: flex;
    padding: 10px 40px;
    color: #fff051;

    .dot-complete {
      font-size: 16px;
      font-weight: bold;
      display: flex;
      align-items: center;
      text-underline-offset: 4px;
      cursor: pointer;
      /* 下划线 */
      &:hover {
        text-decoration: underline;
      }
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 0;
    margin: 0;
    width: 100%;
    color: #fff;
    /* padding: 10px; */
    padding:  10px 30px;
    box-sizing: border-box;
    color: #fff;
    .content-item {
      display: flex;
      width: 100%;
      align-items: center;
      gap: 10px;
      /* justify-content: space-between; */

      .dot-incomplete {
        width: 8px;
        height: 8px;
        min-width: 8px;
        background: #fff;
        border-radius: 50%;
      }

      .dot-complete {
        color: #e3e3e3;
        opacity: 0.5;
      }

      &.completed {
        color: #e3e3e3; // Gray text for completed tasks
        opacity: 0.5;
      }

      .check-text {
        color: #fff051;
        font-size: 16px;
        font-weight: 900;
        text-underline-offset: 4px;
        cursor: pointer;
        text-decoration: underline;
      }
    }

    .confirm {
      color: #fff051;
      text-underline-offset: 4px;
      cursor: pointer;
      font-weight: 900;
      text-decoration: underline;
    }
  }
`;
