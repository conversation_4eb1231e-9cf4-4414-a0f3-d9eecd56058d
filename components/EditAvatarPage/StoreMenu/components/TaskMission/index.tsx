import { forwardRef, useImperativeHandle, Fragment, useRef } from "react";
import { TaskMissionContainer } from "./TaskMIssion.styles";
import Image from "next/image";
import useRewards from "../TaskReward";
import { useTaskContext } from "contexts/TaskContext";
import { TaskSubItem } from "components/EditAvatarPage/StoreMenu/components/TaskCard";
import CheckButton from "../TaskCheck";
import { claimTaskReward, getTaskList } from "@/server";
import toast from "react-hot-toast";

const taskImg = {
  active: "/image/task/active.png",
  completed: "/image/task/completed.png",
};

// 扩展TaskSubItem接口，添加type属性
interface ExtendedTaskItem extends TaskSubItem {
  type?: string;
}

export interface TaskMissionRef {
  refreshTasks: () => void;
}

// 修改接口，只需要传入一个可选的选择器属性
interface TaskMissionProps {
  taskSelector?: string; // 如果提供则显示特定任务，否则显示所有任务
}

const TaskMission = forwardRef<TaskMissionRef, TaskMissionProps>(
  (props, ref) => {
    const { taskSelector } = props;

    // 使用TaskContext
    const {
      trackedTasks,
      refreshTasks,
      onCheckTask,
      loading,
      syncTaskProgress,
      removeTask,
      incentivesConfig,
    } = useTaskContext();

    const taskIdRef = useRef<string>("");

    // 暴露刷新方法给父组件
    useImperativeHandle(ref, () => ({
      refreshTasks,
    }));

    const { Rewards, rewardsRef, setLoading } = useRewards({
      onClaimReward,
    });

    async function onClaimReward() {
      try {
        setLoading(true);
        const res = await claimTaskReward({ id: taskIdRef.current });
        if (res.data.code === 1) {
          // onClaim();
          toast.success("Claimed reward successfully!");
          const result = await getTaskList();
          if (result.data.code === 1) {
            const list = result.data.data;
            syncTaskProgress(list, incentivesConfig);
            removeTask(taskIdRef.current);
            taskIdRef.current = "";
            setLoading(false);
            rewardsRef.current?.close();
          }
        } else {
          toast.error(res.data.msg);
          taskIdRef.current = "";
        }
      } catch (error) {
        console.log("Error claiming reward:", error);
      }
    }

    // 如果没有正在追踪的任务
    if (trackedTasks.length === 0) {
      return null;
    }

    // 如果指定了任务选择器，只显示该任务
    const selectedTask = taskSelector
      ? trackedTasks.find((task) => task.id === taskSelector)
      : trackedTasks[0]; // 否则显示第一个任务

    if (!selectedTask) {
      return null;
    }

    // 检测任务是否完成
    const isTaskCompleted = (task: TaskSubItem) => {
      return task.isCompleted || task.currentProgress >= task.totalProgress;
    };

    // 检测所有任务是否完成
    const areAllTasksCompleted = () => {
      return selectedTask.status === "completed";
    };

    // 检测所有任务是否完成
    const allCompleted = areAllTasksCompleted();

    // 排序：未完成的排在最前面
    const sortedTasks = [...selectedTask.taskItems].sort((a, b) => {
      if (isTaskCompleted(a) && !isTaskCompleted(b)) {
        return 1;
      }
      return -1;
    });

    return (
      <>
        <TaskMissionContainer>
          {/* 任务标题 */}
          <div className="title">
            {/* 图标 */}
            <Image
              src={allCompleted ? taskImg.completed : taskImg.active}
              className="icon"
              alt="任务图标"
              width={48}
              height={48}
            />
            {/* 任务名称 */}
            <h3 className="text">{selectedTask.title}</h3>
          </div>

          {/* 如果所有任务都已完成，显示Completed标志 */}
          {allCompleted ? (
            <div className="all-completed">
              <div
                className="dot-complete"
                onClick={() => {
                  taskIdRef.current = selectedTask.id;
                  rewardsRef.current?.open({
                    title: selectedTask.title,
                    rewardList: selectedTask?.rewards ?? [],
                    count: 1,
                  });
                }}
              >
                ✓ Completed
              </div>
            </div>
          ) : (
            /* 否则显示任务列表 */
            <ul className="content">
              {sortedTasks.map((task, index) => {
                const completed = isTaskCompleted(task);
                const taskWithType = task as ExtendedTaskItem;
                return (
                  <Fragment key={index}>
                    <li
                      className={`content-item ${completed ? "completed" : ""}`}
                    >
                      {completed ? (
                        <div className="dot-complete">✓</div>
                      ) : (
                        <div className="dot-incomplete" />
                      )}
                      <div>{task.description}</div>
                      <div>
                        <span>{task.currentProgress}</span> /{" "}
                        <span>{task.totalProgress}</span>
                      </div>
                      {task.check && task.currentProgress !== task.totalProgress && (
                        <CheckButton
                          taskId={task.id ?? ""}
                          clientRefresh={task.clientRefresh}
                          onCheck={onCheckTask}
                          isLoading={loading}
                        />
                      )}
                    </li>
                    {taskWithType.type && (
                      <li className="content-item confirm">Click To Confirm</li>
                    )}
                  </Fragment>
                );
              })}
            </ul>
          )}
        </TaskMissionContainer>
        <Rewards />
      </>
    );
  }
);

TaskMission.displayName = "TaskMission";

export default TaskMission;
