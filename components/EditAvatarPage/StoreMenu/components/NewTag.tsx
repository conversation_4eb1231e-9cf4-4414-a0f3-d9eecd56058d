import { motion } from "motion/react";

const NewTag = () => {
  return (
    <motion.div
      style={{
        position: "absolute",
        top: "-12px",
        right: "-18px",
        backgroundColor: "#FFCC00",
        color: "white",
        fontSize: "16px",
        fontWeight: "bold",
        borderRadius: "50px",
        zIndex: 20,
        border: "1px solid #000000",
        letterSpacing: "0.5px",
        width: "48px",
        height: "30px",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        transformOrigin: "center center",
        boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.25)",
      }}
      initial={{
        rotate: 30,
        scale: 1,
      }}
      animate={{
        rotate: 30, // 保持30度倾斜
        scale: [1, 1.05, 1], // 只缩放不改变旋转角度
      }}
      transition={{
        duration: 0.8,
        repeat: Infinity,
        repeatType: "loop",
        ease: "easeInOut",
      }}
    >
      NEW
    </motion.div>
  );
};

export default NewTag;
