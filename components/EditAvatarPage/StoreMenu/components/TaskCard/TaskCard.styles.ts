import styled from "styled-components";
import { motion } from "motion/react";

// 卡片状态类型
type CardStatus = "active" | "tracking" | "completed";

// 根据状态返回颜色
const getStatusColor = (status: CardStatus) => {
  switch (status) {
    case "active":
      return "#FFFFFF";
    case "tracking":
      return "#E8BD78";
    case "completed":
      return "#FF8316";
    default:
      return "#FFFFFF";
  }
};

// 根据状态返回标签颜色
const getTagColor = (status: CardStatus) => {
  switch (status) {
    case "active":
      return "#FF8316";
    case "tracking":
      return "#E8BD78";
    case "completed":
      return "#878787";
    default:
      return "#FF8316";
  }
};

// 卡片容器
export const CardContainer = styled.div<{ status: CardStatus }>`
  position: relative;
  background-color: #ffffff;
  border-radius: 24px;
  border: 1px solid #d5c4af;
  padding: 14px 10px;
  width: 100%;
  margin-bottom: 24px;
  margin-top: 4px;
  box-shadow: inset 0px -6px 0px 0px rgba(0, 0, 0, 0.25);
  box-sizing: border-box;

  /* 添加高亮效果，仅当状态为completed时 */
  ${({ status }) =>
    status === "completed" &&
    `
    border: 2px solid #FF8316;
    box-shadow: inset 0px -3px 0px 0px rgba(0, 0, 0, 0.25), 0px 0px 4px 2px rgba(255, 131, 22, 1);
  `}
`;

// 卡片内容
export const CardContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;

  .progress-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .rewards-container {
    display: flex;
    gap: 16px;
    align-items: center;
  }

  .reward-icon {
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0px 1.5px 5.9px rgba(0, 0, 0, 0.15);
  }
`;

// 任务标题区域
export const TaskTitle = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 8px;
  flex-direction: column;

  .time-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 4px 8px;
    border: 1px solid #cabfab;
    border-radius: 12px;
    background-color: #ffffff;
    gap: 4px;
  }

  .time {
    font-family: "JetBrains Mono", monospace;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    color: #140f08;
  }

  .title-text {
    color: #140f08;
    margin: 0;
    line-height: 1.24;
    font-size: 20px;
    text-shadow: 0 0 black;
  }
`;

export const TaskDescription = styled.div`
  color: #a58061;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.24;
`;

// 倒计时组件
export const TimeRemaining = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  border: 1px solid #cabfab;
  border-radius: 12px;
  background-color: #ffffff;

  span {
    font-family: "JetBrains Mono", monospace;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    color: #140f08;
  }
`;

// NEW标签
export const NewBadge = styled.div`
  position: absolute;
  top: -15px;
  left: -15px;
  width: 48px;
  height: 48px;
`;

// 进度项容器
export const ProgressItemContainer = styled.div<{ isCompleted: boolean }>`
  display: flex;
  align-items: start;
  gap: 11px;
  padding: 10px;
  background-color: #fbf4e8;
  border-radius: 12px;
  flex-direction: column;

  .progress {
    font-size: 14px;
    font-weight: 500;
    color: #140f08;
    display: flex;
    align-items: flex-start;
    /* gap: 8px; */
    position: relative;
    overflow: hidden;

    &.completed {
      color: #878787;
      text-decoration: line-through;
    }

    .text-wrapper {
      display: inline;
      flex: 1;
      word-break: break-word;
      .check-icon {
        color: #878787;
        font-size: 16px;
        margin-right: 6px;
        align-self: flex-start;
      }

      .dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #140f08;
        display: inline-block;
        margin: 2px 6px 2.5px 0px;
      }
    }

    .task-description {
      white-space: pre-line;
      overflow: visible;
      display: inline;
      word-break: break-all;
    }

    .task-progress {
      white-space: nowrap;
      display: inline;
      margin-left: 4px;
    }

    .check-text {
      color: #ff7a27;
      font-size: 14px;
      font-weight: 900;
      text-underline-offset: 4px;
      cursor: pointer;
      text-decoration: underline;
      display: inline;
      margin-left: 4px;
    }
  }
`;
