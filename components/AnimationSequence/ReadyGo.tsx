import ReadyImg from "/public/image/ready.png";
import GoImg from "/public/image/go.png";
import Image from "next/image";
import styled from "styled-components";
import { motion, AnimatePresence } from "framer-motion";
import { useState, useEffect } from "react";

const ImageContainer = styled(motion.div)`
  position: absolute;
`;

interface ReadyGoProps {
  onComplete?: (timestamp?: number) => void;
  readyDuration?: number; // 添加自定义Ready显示时长
  goDuration?: number; // 添加自定义Go显示时长
}

const ReadyGo: React.FC<ReadyGoProps> = ({
  onComplete,
  readyDuration = 2000,
  goDuration = 2000,
}) => {
  const [showReady, setShowReady] = useState(true);
  const [showGo, setShowGo] = useState(false);
  const [visible, setVisible] = useState(true);

  // 放大模糊消失的动画配置
  const expandBlurExit = {
    opacity: 0,
    scale: 3,
    filter: "blur(18px)",
  };

  useEffect(() => {
    // readyDuration后隐藏Ready并显示Go
    const readyTimer = setTimeout(() => {
      setShowReady(false);

      // 添加短暂延迟以便Ready的退出动画完成后再显示Go
      setTimeout(() => {
        setShowGo(true);
      }, 300);
    }, readyDuration);

    // Go图片显示后再过goDuration时间隐藏
    const goTimer = setTimeout(() => {
      setShowGo(false);

      // 给Go退出动画留时间，然后再通知完成
      setTimeout(() => {
        setVisible(false);
        if (onComplete) {
          // // 获取当前时间戳
          // const timestamp = Date.now();
          onComplete();
        }
      }, 300);
    }, readyDuration + 300 + goDuration);

    return () => {
      clearTimeout(readyTimer);
      clearTimeout(goTimer);
    };
  }, [onComplete, readyDuration, goDuration]);

  if (!visible) return null;

  return (
    <AnimatePresence>
      {showReady && (
        <ImageContainer
          key="ready"
          initial={{ y: 100, opacity: 0, scale: 0.8 }}
          animate={{ y: 0, opacity: 1, scale: 1 }}
          exit={expandBlurExit}
          transition={{
            duration: 0.5,
            exit: { duration: 0.4 },
          }}
        >
          <Image src={ReadyImg} alt="Ready" width={600} height={400} />
        </ImageContainer>
      )}

      {showGo && (
        <ImageContainer
          key="go"
          initial={{ y: 100, opacity: 0, scale: 0.8 }}
          animate={{ y: 0, opacity: 1, scale: 1 }}
          exit={expandBlurExit}
          transition={{
            duration: 0.5,
            exit: { duration: 0.4 },
          }}
        >
          <Image src={GoImg} alt="Go" width={600} height={400} />
        </ImageContainer>
      )}
    </AnimatePresence>
  );
};

export default ReadyGo;
