/**
 * 实现轮播图包装器
 */
import Slider, { Settings } from "react-slick";
import "slick-carousel/slick/slick.css";
// import "slick-carousel/slick/slick-theme.css";
import styled from "styled-components";

const CarouselContainer = styled.div`
  .tree-bar-slider {
    width: 280px;
  }
`;

export default function TreeBarCarousel({
  children,
}: {
  children: React.ReactNode;
}) {
  const settings: Settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 5000,
    arrows: false,
    className: "tree-bar-slider",
  };
  return (
    <CarouselContainer>
      <Slider {...settings}>
        {children}
      </Slider>
    </CarouselContainer>
  );
}
