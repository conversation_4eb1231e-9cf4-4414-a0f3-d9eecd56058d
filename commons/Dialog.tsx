import { AnimatePresence, motion } from "motion/react";
import { useState, useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import styled, { createGlobalStyle } from "styled-components";

// 全局样式，防止 body 滚动穿透
const GlobalStyle = createGlobalStyle<{ isOpen: boolean }>`
  body {
    overflow: ${(props) => (props.isOpen ? "hidden" : "auto")};
    padding-right: ${(props) => (props.isOpen ? "15px" : "0")};
  }
`;

const typeMap = {
  快速下落回弹: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring", // 使用弹簧物理模型
      mass: 1, // 质量
      damping: 15, // 阻尼
      stiffness: 200, // 刚度
      velocity: 10, // 初始速度
      bounceStiffness: 200, // 回弹刚度
      bounceDamping: 15, // 回弹阻尼
    },
  },
  下落挤压缩放: {
    y: 0,
    opacity: 1,
    scale: [1, 0.9, 1.05, 0.95, 1],
    transition: {
      scale: {
        times: [0, 0.5, 0.7, 0.9, 1],
        duration: 0.8,
      },
    },
  },
};

const ModalOverlay = styled(motion.div)<{ $isVisible: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: ${({ $isVisible }) => ($isVisible ? "1000" : "-1")};
  pointer-events: ${({ $isVisible }) => ($isVisible ? "auto" : "none")};
`;

interface DialogProps {
  isOpen: boolean;
  onClose?: () => void;
  children: React.ReactNode;
  closeOnOverlayClick?: boolean;
  width?: string;
  height?: string;
  className?: string;
  style?: React.CSSProperties;
}

const Dialog = ({
  isOpen,
  onClose,
  children,
  closeOnOverlayClick = true,
  width,
  height,
  className,
  style,
}: DialogProps) => {
  const [isMounted, setIsMounted] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  // 添加键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose?.();
      }
    };

    if (isOpen) {
      window.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [isOpen, onClose]);

  // 处理遮罩层点击
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (closeOnOverlayClick) {
      onClose?.();
    }
  };



  // 定义模拟球体下落击地回弹的动画变体
  const bounceVariants = {
    // 初始状态：在视口上方
    hidden: {
      y: "-70vh",
      opacity: 0,
    },
    // 可见状态：模拟下落和回弹
    visible: {
      ...typeMap["快速下落回弹"],
    },
    // 退出状态：向上弹出并消失
    exit: {
      y: "-70vh",
      opacity: 0,
      transition: {
        duration: 0.3,
        ease: "easeInOut",
      },
    },
  };

  if (!isMounted) {
    return null;
  }

  return createPortal(
    <>
      <GlobalStyle isOpen={isOpen} />
      <AnimatePresence
        mode="wait"
        onExitComplete={() => {
          // This will run after all exit animations complete
          // No need to do anything here, just ensuring proper cleanup
        }}
      >
        {isOpen && (
          <ModalOverlay
            tabIndex={-1}
            ref={modalRef}
            onClick={handleOverlayClick}
            initial="hidden"
            animate="visible"
            exit="hidden"
            variants={{
              hidden: { opacity: 0, pointerEvents: "none" },
              visible: { opacity: 1, pointerEvents: "auto" },
            }}
            transition={{ duration: 0.1 }}
            $isVisible={isOpen}
          >
            <motion.div
              ref={contentRef}
              variants={bounceVariants}
              initial="hidden"
              animate={isOpen ? "visible" : "exit"}
              exit="exit"
              onClick={(e) => e.stopPropagation()}
              style={{
                width,
                height,
                ...style,
              }}
              className={className}
            >
              {children}
            </motion.div>
          </ModalOverlay>
        )}
      </AnimatePresence>
    </>,
    document.body as HTMLElement,
    "dialog"
  ) as React.ReactNode;
};

export default Dialog;
