import {forwardRef, Ref, useImperativeHandle, useRef, useState} from "react";
import SettingSvg from "/public/image/setting.svg";
import Image from "next/image";
import styled from "styled-components";
import Dialog from "./Dialog";
import {motion} from "framer-motion";
import Slider from "./Slider";

const SettingIcon = styled.div`
  width: 24px;
  height: 24px;
  cursor: pointer;
  border-radius: 10px;
  background-color: transparent;
  padding: 10px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #fc6b18;
    border-radius: 10px;
  }
`;

export default function Setting() {
  const settingModalRef = useRef<SettingModalRef>(null);
  return (
    <>
      <SettingIcon
        onClick={() => {
          settingModalRef.current?.open();
        }}
      >
        <Image src={SettingSvg} alt="setting" width={24} height={24} />
      </SettingIcon>
      <SettingModal ref={settingModalRef} />
    </>
  );
}

const DialogContent = styled.div`
  position: relative;
  width: 600px;
  /* height: 400px; */

  &:before {
    content: "";
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 44px; // 40px + 4px border
    border: 4px solid #14110a;
    z-index: 1;
  }
`;

const Content = styled.div`
  position: relative;
  background-color: #fef1df;
  border-radius: 40px;
  border: 8px solid #e8901c;
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: start;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  z-index: 2;
  padding: 0px 30px;
`;

const Title = styled.div`
  font-size: 20px;
  font-weight: 700;
  color: #14110a;
  width: 100%;
  text-align: center;
  padding-top: 18px;
  padding-bottom: 12px;
`;

const MenuContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between; // 改为space-between让左右两侧分开
  width: 100%;
  padding: 24px 44px;
  background-color: #f5e5c7;
  border-radius: 28px;
  box-shadow: inset 0 0px 20px rgba(0, 0, 0, 0.15); // 添加内阴影制造凹陷感
  margin-bottom: 14px;
  box-sizing: border-box;

  .left {
    display: flex;
    flex-direction: column;
    gap: 20px; // 设置标题之间的间距

    p {
      font-size: 18px;
      color: #14110a;
      margin: 0;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    gap: 20px; // 与左侧标题对应的间距
  }
`;

const MaxPlayersContent = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 100%;
  padding: 24px 44px;
  background-color: #f5e5c7;
  border-radius: 28px;
  box-shadow: inset 0 0px 20px rgba(0, 0, 0, 0.15); // 添加内阴影制造凹陷感
  box-sizing: border-box;
  margin-bottom: 20px;
  .max-players-content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    .title {
      font-size: 18px;
      color: #14110a;
    }
    .value {
      font-size: 14px;
      color: #a58061;
      padding: 0;
      margin: 0;
      .value-number {
        font-size: 18px;
        color: #14110a;
      }
    }
  }
`;

// 添加开关按钮组件
const ToggleSwitch = styled.label`
  position: relative;
  display: inline-block;
  width: 60px;
  height: 30px;

  input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #c2b8a2;
    transition: 0.4s;
    border-radius: 34px;

    &:before {
      position: absolute;
      content: "";
      height: 22px;
      width: 22px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: 0.4s;
      border-radius: 50%;
    }
  }

  input:checked + .slider {
    background-color: #e8901c;
  }

  input:checked + .slider:before {
    transform: translateX(30px);
  }
`;

const Footer = styled(motion.div)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 20px;
  margin-top: auto; // 将按钮推到底部
  padding-bottom: 24px;

  .cancel {
    color: #fff;
    background-color: #c1af9c;
    width: 200px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    box-shadow: 0 4px 0 #a39484; // 底部阴影创造立体感
    transition: all 0.1s ease;

    &:active {
      transform: translateY(4px);
      box-shadow: 0 0 0 #a39484;
    }
  }

  .confirm {
    color: #fff;
    background-color: #fc7922;
    width: 200px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    box-shadow: 0 4px 0 #d65b0c; // 底部阴影创造立体感
    transition: all 0.1s ease;

    &:active {
      transform: translateY(4px);
      box-shadow: 0 0 0 #d65b0c;
    }
  }
`;

interface SettingModalRef {
  open: () => void;
}

const SettingModal = forwardRef<SettingModalRef>(
  (props, ref: Ref<SettingModalRef>) => {
    const [settings, setSettings] = useState(() => {
      // 使用typeof window检查是否在浏览器环境
      if (typeof window !== "undefined") {
        const specialEffects = localStorage.getItem("specialEffects");
        if (specialEffects === null) {
          localStorage.setItem("specialEffects", "false");
        }

        const music = localStorage.getItem("music");
        if (music === null) {
          localStorage.setItem("music", "true");
        }

        const soundEffects = localStorage.getItem("soundEffects");
        if (soundEffects === null) {
          localStorage.setItem("soundEffects", "true");
        }

        const onlineMultiplayer = localStorage.getItem("onlineMultiplayer");
        if (onlineMultiplayer === null) {
          localStorage.setItem("onlineMultiplayer", "true");
        }

        const maxPlayers = localStorage.getItem("maxPlayers");
        if (maxPlayers === null) {
          localStorage.setItem("maxPlayers", "10");
        }

        return {
          // specialEffects: localStorage.getItem("specialEffects") === "true",
          music: localStorage.getItem("music") === "true",
          soundEffects: localStorage.getItem("soundEffects") === "true",
          onlineMultiplayer:
            localStorage.getItem("onlineMultiplayer") === "true",
          maxPlayers:
            parseInt(localStorage.getItem("maxPlayers") || "10"),
        };
      }
      // 服务器端渲染时返回默认值
      return {
        // specialEffects: false,
        music: true,
        soundEffects: true,
        onlineMultiplayer: true,
        maxPlayers: 0,
      };
    });

    const [isOpen, setIsOpen] = useState(false);

    // 打开面板时刷新设置
    useImperativeHandle(ref, () => ({
      open() {
        // 重新读取设置，确保显示最新状态
        setSettings({
          // specialEffects: localStorage.getItem("specialEffects") === "true",
          music: localStorage.getItem("music") === "true",
          soundEffects: localStorage.getItem("soundEffects") === "true",
          onlineMultiplayer:
            localStorage.getItem("onlineMultiplayer") === "true",
          maxPlayers:
            localStorage.getItem("maxPlayers") === "0"
              ? 0
              : parseInt(localStorage.getItem("maxPlayers") || "0"),
        });
        setIsOpen(true);
      },
    }));

    const handleToggle = (setting: keyof typeof settings) => {
      setSettings((prev) => ({
        ...prev,
        [setting]: !prev[setting],
      }));
    };

    // 添加动画变体
    const containerVariants = {
      hidden: {},
      visible: {
        transition: {
          staggerChildren: 0.25, // 子元素依次动画的间隔
        },
      },
    };

    const leftItemVariants = {
      hidden: { x: -50, opacity: 0 }, // 初始位置
      visible: {
        x: 0, // 最终位置
        opacity: 1, // 最终透明度
        transition: {
          type: "spring", // 使用spring动画
          stiffness: 150, // 弹性系数
          damping: 6, // 阻尼系数
        },
      },
    };

    const rightItemVariants = {
      hidden: { x: 50, opacity: 0 }, // 初始位置
      visible: {
        x: 0, // 最终位置
        opacity: 1, // 最终透明度
        transition: {
          type: "spring", // 使用spring动画
          stiffness: 150, // 弹性系数
          damping: 6, // 阻尼系数
        },
      },
    };

    const footerVariants = {
      hidden: { y: 50, opacity: 0 }, // 初始位置
      visible: {
        y: 0, // 最终位置
        opacity: 1, // 最终透明度
        transition: {
          type: "spring", // 使用spring动画
          stiffness: 150, // 弹性系数
          damping: 6, // 阻尼系数
          delay: 0.3, // 等待内容项动画后再显示
        },
      },
    };

    const onConfirm = () => {
      try {
        // 保存设置
        // localStorage.setItem(
        //   "specialEffects",
        //   settings.specialEffects ? "true" : "false"
        // );
        localStorage.setItem("music", settings.music ? "true" : "false");
        localStorage.setItem(
          "soundEffects",
          settings.soundEffects ? "true" : "false"
        );
        localStorage.setItem(
          "onlineMultiplayer",
          settings.onlineMultiplayer ? "true" : "false"
        );

        localStorage.setItem("maxPlayers", settings.maxPlayers.toString());

        // 可以添加一个自定义事件通知其他组件设置已更改
        const event = new CustomEvent("settingsChanged", {
          detail: settings,
        });
        window.dispatchEvent(event);

        setIsOpen(false);
      } catch (error) {
        // 处理存储错误
        console.error("Failed to save settings:", error);
      }
    };

    // 添加取消时的处理
    const onCancel = () => {
      // 重置为存储的设置
      setSettings({
        // specialEffects: localStorage.getItem("specialEffects") === "true",
        music: localStorage.getItem("music") === "true",
        soundEffects: localStorage.getItem("soundEffects") === "true",
        onlineMultiplayer: localStorage.getItem("onlineMultiplayer") === "true",
        maxPlayers:
          localStorage.getItem("maxPlayers") === "0"
            ? 0
            : parseInt(localStorage.getItem("maxPlayers") || "0"),
      });
      setIsOpen(false);
    };

    // 过滤maxPlayers
    const settingsList = Object.keys(settings).filter(
      (setting) => setting !== "maxPlayers"
    );

    return (
      <Dialog isOpen={isOpen} onClose={onCancel}>
        <DialogContent>
          <Content>
            <Title>Settings</Title>
            <MenuContent>
              <motion.div
                className="left"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                {[
                  // "Visual Effects",
                  "Background Music",
                  "Sound Effects",
                  "Online Multiplayer",
                ].map((text, index) => (
                  <motion.p key={text} variants={leftItemVariants}>
                    {text}
                  </motion.p>
                ))}
              </motion.div>
              <motion.div
                className="right"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                {settingsList.map((setting, index) => (
                  <motion.div key={setting} variants={rightItemVariants}>
                    <ToggleSwitch>
                      <input
                        type="checkbox"
                        checked={
                          settings[setting as keyof typeof settings] as boolean
                        }
                        onChange={() =>
                          handleToggle(setting as keyof typeof settings)
                        }
                      />
                      <span className="slider"></span>
                    </ToggleSwitch>
                  </motion.div>
                ))}
              </motion.div>
            </MenuContent>

            <MaxPlayersContent>
              {/* 最大玩家数量 */}
              <div className="max-players-content">
                <span className="title">Max Players</span>
                <p className="value">
                  Players: <span className="value-number"> {settings.maxPlayers}</span>
                </p>
              </div>
              {/* slider: 0 - 30 0最小值，30最大值 */}
              <Slider
                min={0}
                max={30}
                value={settings.maxPlayers}
                onChange={(v) => setSettings((prev) => ({ ...prev, maxPlayers: v }))}
              />
            </MaxPlayersContent>
            <Footer
              initial="hidden"
              animate="visible"
              variants={footerVariants}
            >
              <motion.div
                className="cancel"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onCancel}
              >
                Cancel
              </motion.div>
              <motion.div
                className="confirm"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onConfirm}
              >
                Confirm
              </motion.div>
            </Footer>
          </Content>
        </DialogContent>
      </Dialog>
    );
  }
);