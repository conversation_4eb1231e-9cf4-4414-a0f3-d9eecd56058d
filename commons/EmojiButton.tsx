import styled from "styled-components";
import EmojiPicker from "./EmojiPicker";
import { useState, forwardRef, useImperativeHandle, Ref } from "react";

const PickerWrapper = styled.div`
  position: relative;
  display: inline-block;
`;

const EmojiButtonContainer = styled.button`
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  margin-right: 4px;
`;

interface EmojiButtonProps {
  onChangeEmoji: (emoji: string) => void;

}
export interface EmojiButtonRef {
  onOpen?: () => void;
  onClose?: () => void;
}
const EmojiButton = forwardRef<EmojiButtonRef, EmojiButtonProps>(
  ({ onChangeEmoji }, ref: Ref<EmojiButtonRef>) => {
    const [isPickerOpen, setIsPickerOpen] = useState(false);
    // const [selectedEmoji, setSelectedEmoji] = useState<string | null>(null);
    const [pickerPosition] = useState<"top" | "bottom" | "left" | "right">(
      "top"
    );
    const handleEmojiSelect = (emoji: any) => {
    //   console.log("Selected emoji:", emoji);
      onChangeEmoji(emoji.native);
    //   setSelectedEmoji(emoji.native);
      setIsPickerOpen(false);
    };
    useImperativeHandle(ref, () => ({
      onOpen: () => setIsPickerOpen(true),
      onClose: () => setIsPickerOpen(false),
    }));
    return (
      <PickerWrapper>
        <EmojiButtonContainer
          onClick={() => {
            setIsPickerOpen(!isPickerOpen);
          }}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#fff"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M22 11v1a10 10 0 1 1-9-10" />
            <path d="M8 14s1.5 2 4 2 4-2 4-2" />
            <line x1="9" x2="9.01" y1="9" y2="9" />
            <line x1="15" x2="15.01" y1="9" y2="9" />
            <path d="M16 5h6" />
            <path d="M19 2v6" />
          </svg>
        </EmojiButtonContainer>
        <EmojiPicker
          isOpen={isPickerOpen}
          onClose={() => setIsPickerOpen(false)}
          onEmojiSelect={handleEmojiSelect}
          theme="dark"
          position={pickerPosition}
          emojiSize={22}
          perLine={9}
          locale="zh"
        />
      </PickerWrapper>
    );
  }
);

EmojiButton.displayName = "EmojiButton";

export default EmojiButton;
