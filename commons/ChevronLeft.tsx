import styled from "styled-components";

const ChevronLeftIconContainer = styled.div`
  width: 34px;
  height: 34px;
  background-color: #fff;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: -26px;
  top: -2px;
`;

const ChevronLeftIcon = () => {
  return (
    <ChevronLeftIconContainer>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
      stroke="#FF8316"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
        <path d="m15 18-6-6 6-6" />
      </svg>
    </ChevronLeftIconContainer>
  );
};

export default ChevronLeftIcon;
