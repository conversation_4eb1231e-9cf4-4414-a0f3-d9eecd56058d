import React, { useRef, useEffect } from "react";
import data from "@emoji-mart/data";
import Picker from "@emoji-mart/react";
import styled from "styled-components";

export interface EmojiPickerProps {
  onEmojiSelect: (emoji: any) => void;
  position?: "top" | "bottom" | "left" | "right";
  theme?: "light" | "dark" | "auto";
  isOpen: boolean;
  onClose: () => void;
  emojiSize?: number;
  perLine?: number;
  showSkinTones?: boolean;
  skinTonePosition?: "preview" | "search" | "none";
  maxFrequentRows?: number;
  locale?: string;
  custom?: any[];
  customCategories?: Record<string, any>;
  autoFocus?: boolean;
  navPosition?: "top" | "bottom" | "none";
  previewEmoji?: string;
}

const EmojiPicker: React.FC<EmojiPickerProps> = ({
  onEmojiSelect,
  position = "top",
  theme = "dark",
  isOpen,
  onClose,
  emojiSize = 20,
  perLine = 8,
  showSkinTones = true,
  skinTonePosition = "preview",
  maxFrequentRows = 4,
  locale = "zh",
  custom = [],
  customCategories,
  autoFocus = false,
  navPosition = "top",
  previewEmoji = "point_up",
}) => {
  const pickerRef = useRef<HTMLDivElement>(null);

  // 处理点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target as Node) &&
        isOpen
      ) {
        onClose();
      }
    };

    // 添加点击外部关闭监听
    document.addEventListener("mousedown", handleClickOutside);

    // 添加ESC键关闭
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        onClose();
      }
    };

    document.addEventListener("keydown", handleEscKey);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  // 计算选择器位置样式
  const getPositionStyle = () => {
    switch (position) {
      case "top":
        return { bottom: "100%", left: "0", "margin-bottom": "8px" };
      case "bottom":
        return { top: "100%", left: "0", "margin-top": "8px" };
      case "left":
        return { right: "100%", top: "0", "margin-right": "8px" };
      case "right":
        return { left: "100%", top: "0", "margin-left": "8px" };
      default:
        return { top: "100%", left: "0", "margin-top": "8px" };
    }
  };

  // emoji-mart 5.x 使用不同的配置方式
  const pickerConfig = {
    data,
    onEmojiSelect,
    theme,
    emojiSize,
    perLine,
    previewPosition: "none",
    skinTonePosition:
      skinTonePosition === "none" ? undefined : skinTonePosition,
    maxFrequentRows,
    locale,
    custom: custom.length > 0 ? custom : undefined,
    categories: customCategories,
    autoFocus,
    navPosition: navPosition === "none" ? undefined : navPosition,
    previewEmoji,
    searchPosition: "none",
    noSearch: true,
  };

  return (
    <PickerContainer
      positionStyle={getPositionStyle()}
      ref={pickerRef}
      data-testid="emoji-picker"
    >
      <Picker {...pickerConfig} />
    </PickerContainer>
  );
};

interface PositionStyle {
  top?: string;
  bottom?: string;
  left?: string;
  right?: string;
  "margin-top"?: string;
  "margin-bottom"?: string;
  "margin-left"?: string;
  "margin-right"?: string;
}

const PickerContainer = styled.div<{ positionStyle: PositionStyle }>`
  position: absolute;
  ${(props) =>
    Object.entries(props.positionStyle)
      .map(([key, value]) => `${key}: ${value};`)
      .join(" ")}
  z-index: 100;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  overflow: hidden;
  width: 350px;
  /* max-width: calc(100vw - 20px); */

  em-emoji-picker {
    --border-radius: 8px;
    --category-emoji-size: 20px;
    height: 350px;
    width: 100%;
    border: none;
  }
`;

export default EmojiPicker;
