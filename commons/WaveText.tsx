import { motion } from "framer-motion";
import React from "react";
import styled from "styled-components";

// text-3xl font-bold text-purple-600 inline-block

const WaveTextContainer = styled(motion.span)`
  font-weight: bold;
  display: inline-block;
  letter-spacing: 0.1em;
`;

interface WaveTextAnimationProps {
  text: string;
}
export const WaveTextAnimation = ({ text }: WaveTextAnimationProps) => {
  const characters = Array.from(text);

  return (
    <div className="flex">
      {characters.map((char, i) => (
        <WaveTextContainer
          key={i}
          animate={{
            y: [0, -10, 0],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: i * 0.1,
            ease: "easeInOut",
          }}
          className=""
        >
          {char === " " ? " " : char}
        </WaveTextContainer>
      ))}
    </div>
  );
};
