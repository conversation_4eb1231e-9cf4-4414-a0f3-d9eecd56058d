import {FATHER_TYPE_ENUM, IChildrenInscription, IFatherInscription, PATH_ID_ENUM} from "./type";
import ActionIcon from '../public/image/menus/action.svg'
import HairIcon from '../public/image/menus/hair.svg'
import PantsIcon from '../public/image/menus/pants.svg'
import ShirtIcon from '../public/image/menus/shirt.svg'
import ShoesIcon from '../public/image/menus/shoes.svg'
import PetIcon from '../public/image/menus/pet.svg'
import {SVG_FILE} from "./staticFile";
import {GLTFLoader} from "three/examples/jsm/loaders/GLTFLoader";

export const THEME_MEDIA_NUM = {
    ORD_BROWSER: 0,//ordinals浏览器显示尺寸
    ORD_PREVIEW: 0,//ordinals浏览器列表预览显示尺寸,unisat钱包显示尺寸
    FRONTEND_LARGE: 0,//官网大屏显示
}
export const THEME_MEDIA_ENUM = {
    ORD_BROWSER: `@media screen and (max-width: ${THEME_MEDIA_NUM.ORD_BROWSER}px)`,//ordinals浏览器显示尺寸
    ORD_PREVIEW: `@media screen and (max-width: ${THEME_MEDIA_NUM.ORD_PREVIEW}px)`,//ordinals浏览器列表预览显示尺寸,unisat钱包显示尺寸
    FRONTEND_LARGE: `@media screen and (min-width: ${THEME_MEDIA_NUM.FRONTEND_LARGE}px)`,//官网大屏显示
}

// 如果为true，表示使用本地资源，上线之前改为false
export const IS_LOCAL_TEST = process.env.USE_CONFIG === 'LOCAL_TEST_CONFIG'


//如果 AVATAR_VERSION参数未定义则是 浏览器版本
export const IS_AVATAR_PAGE = typeof process.env.AVATAR_VERSION === 'undefined'

// nft图浏览器接口地址 目前用在贴图
export const ORD_NFT_IMG_SERVER: string = process.env.ORD_NFT_IMG_SERVER as string


// 当前是否为测试环境
export const IS_TEST_ENV = process.env.ENVIRONMENT !== 'online' && process.env.ENVIRONMENT !== 'release'

// ord浏览器根域名
const ORD_SERVER = process.env.ORD_SERVER
// ord浏览器根域名
const CDN_SERVER = process.env.CDN_SERVER || ''
const CDN_VERSION = process.env.CDN_VERSION || 'v0.0.0'

function loadAvatarModel(element: IFatherInscription, loaded: () => void) {
    let typeName = String(element.type)
    if (typeName === FATHER_TYPE_ENUM.Hat) {
        typeName = 'Heat';
    }

    let index = element.childrenInscription.length + 1
    const iconPath = `./assets/${typeName}/icon_${typeName}_` + index.toString().padStart(2, '0') + `.png`;
    const glbPath = `./assets/${typeName}/${typeName}_` + index.toString().padStart(2, '0') + `.glb`;
    const loader = new GLTFLoader();

    loader.load(glbPath, (gltf) => {
        element.childrenInscription.push({
            "inscriptionId": glbPath,
            "metadata": {
                "collection": "uniworlds_" + typeName,
                "version": "v0.0.1",
                "icon": iconPath
            }
        })
        loadAvatarModel(element, loaded)
    }, undefined, (error) => {
        console.error('no exist glb ', glbPath)
        loaded()
    })
}

// 本地资源测试配置
const LOCAL_TEST_CONFIG = {
    // avatar铭文id
    AVATAR_INSCRIPTION_ID: {
        Body: './assets/MainBody.glb',
        Hat: './assets/Heat/Heat_00.glb',
        Pants: './assets/Pants/Pants_00.glb',
        Shirt: './assets/Shirt/Shirt_00.glb',
        Shoes: './assets/Shoes/Shoes_00.glb',
    },
    // 铭文配置
    DEFAULT_FATHER_INSCRIPTION: [
        {
            type: FATHER_TYPE_ENUM.Action,//类型名称
            pathIdKey: PATH_ID_ENUM.actionId,//对应IAvatarMetadata，用来对应渲染部件
            "inscriptionId": ActionIcon.src,
            "childrenInscription": []
        },
        {
            type: FATHER_TYPE_ENUM.Pet,//类型名称
            pathIdKey: PATH_ID_ENUM.petId,//对应IAvatarMetadata，用来对应渲染部件
            "inscriptionId": PetIcon.src,
            "childrenInscription": []
        },
        {
            type: FATHER_TYPE_ENUM.Pants,
            pathIdKey: PATH_ID_ENUM.pantsId,
            "inscriptionId": PantsIcon.src,
            "childrenInscription": []
        },
        {
            type: FATHER_TYPE_ENUM.Hat,
            pathIdKey: PATH_ID_ENUM.hatId,
            "inscriptionId": HairIcon.src,
            "childrenInscription": []
        },
        {
            type: FATHER_TYPE_ENUM.Shoes,
            pathIdKey: PATH_ID_ENUM.shoesId,
            "inscriptionId": ShoesIcon.src,
            "childrenInscription": []
        },
        {
            type: FATHER_TYPE_ENUM.Shirt,
            pathIdKey: PATH_ID_ENUM.shirtId,
            "inscriptionId": ShirtIcon.src,
            "childrenInscription": [],
            // 支持设置贴图
            texture: {
                pathIdKey: PATH_ID_ENUM.shirtTextureId,//对应IAvatarMetadata的key，用来对应渲染部件
            },
            // 支持设置颜色
            color: {
                pathIdKey: PATH_ID_ENUM.shirtColor,//对应IAvatarMetadata的key，用来对应渲染部件
            }
        },
    ] as IFatherInscription[]
}
// 本地链测试链配置
const CHAIN_TEST_CONFIG = {
    AVATAR_INSCRIPTION_ID: {
        Body: '181025d53ddba02dd1585deb1977d14c573ba24b36498e42c8ee8b566edf62d3i0',
        Hat: 'a650c6f70cfb813007fbd660d65c196ee463adc7de40a67de4af429656339a18i0',
        Pants: '84ab07ccd5f890db191972a237893827841ef05bd9ff30c384c9bdc7917e38dci0',
        Shirt: '8947f0d00a095395cfbd0d9bb1adc1d269dfd814e1a3cf4d2f737f91426e8c37i0',
        Shoes: '12a0cb4074ab96d1bd664d464a84949d81fdb55cdba2335dc1fc37ed3de525d0i0',
    },
    DEFAULT_FATHER_INSCRIPTION: [
        {
            type: FATHER_TYPE_ENUM.Action,//类型名称
            pathIdKey: PATH_ID_ENUM.actionId,//对应IAvatarMetadata，用来对应渲染部件
            inscriptionId: '31b64e4ed39df2ba11462bba3ca989a3788739f2f40ea61bf3526c87421f1ecci0',// 父铭文id
            childrenInscription: []//子铭文 会去查父铭文下的所有子铭文
        },
        {
            type: FATHER_TYPE_ENUM.Pants,
            pathIdKey: PATH_ID_ENUM.pantsId,
            inscriptionId: 'ff421c9ebefea1fb66afd4c5da848d6b4f1b4e99dae39f7dc9d2e5a8d1463bf0i0',
            childrenInscription: []
        },
        {
            type: FATHER_TYPE_ENUM.Hat,
            pathIdKey: PATH_ID_ENUM.hatId,
            inscriptionId: '3b4011c172cbb2dd9ca58927b50a80c7d7b8b9206d65d99658e278001b814c61i0',
            childrenInscription: []
        },
        {
            type: FATHER_TYPE_ENUM.Shoes,
            pathIdKey: PATH_ID_ENUM.shoesId,
            inscriptionId: '1209f9d85732395bfc4debbcb8c8955d2a443e57974f6727fbe03ab5cca50c56i0',
            childrenInscription: []
        },
        {
            type: FATHER_TYPE_ENUM.Shirt,
            pathIdKey: PATH_ID_ENUM.shirtId,
            inscriptionId: 'c9a0ac1c3b502c50e7a225e91f8e40ff50944ae27b36d1896cc0e44185bdd86ai0',
            childrenInscription: [],
            // 支持设置贴图
            texture: {
                pathIdKey: PATH_ID_ENUM.shirtTextureId,//对应IAvatarMetadata的key，用来对应渲染部件
            },
            // 支持设置颜色
            color: {
                pathIdKey: PATH_ID_ENUM.shirtColor,//对应IAvatarMetadata的key，用来对应渲染部件
            }
        },
        {
            type: FATHER_TYPE_ENUM.Pet,//类型名称
            pathIdKey: PATH_ID_ENUM.petId,//对应IAvatarMetadata，用来对应渲染部件
            inscriptionId: "c86687007c50d6462173fc4fd420beca2bb2d62cf116c7546207c675b742d161i0",
            childrenInscription: []
        },
        // {
        //   type: FATHER_TYPE_ENUM.ShirtTexture,
        //   pathIdKey: PATH_ID_ENUM.shirtTextureId,
        //   inscriptionId: '92dc4f05a8e76fdf39fe35b4e3dbfce70c5757ed068184d6ce07a7b8ea6eaf49i0',
        //   childrenInscription: []
        // }
    ] as IFatherInscription[]
}
// 线上正式环境配置
const CHAIN_PRO_CONFIG = {
    TEST_USER_AVATAR_INSCRIPTION_ID: '',//置空
    AVATAR_INSCRIPTION_ID: {
        Body: '6165a1e71dfcf93feb546ce8a360e5de7da581cbb6a1bc136bd40cfa3f6e81abi0',
        Hat: '780ebf69015f62ca46c2cd231de5fe5173e99fda4a7a7519b3683b0978cf1e34i0',
        Pants: 'f99933c9784f2d25258572689b07f9d5735b74ee8ffff3f1b38b383bafa28729i0',
        Shirt: 'c72a8be9b1617bc2247a992ec3266a94c075559e14a5c56865da8eab15ed9375i0',
        Shoes: 'b6b74aaea1fa0be056f243c2afc209442b25008e492b04367c41329d35e90e08i0',
    },
    DEFAULT_FATHER_INSCRIPTION: [
        {
            type: FATHER_TYPE_ENUM.Action,//类型名称
            pathIdKey: PATH_ID_ENUM.actionId,//对应IAvatarMetadata，用来对应渲染部件
            inscriptionId: '4d0b7b020147d1d70ce107b7a365de1bdca92ba13d00631c46a58920096d33e9i0',// 父铭文id
            childrenInscription: []//子铭文 会去查父铭文下的所有子铭文
        },
        {
            type: FATHER_TYPE_ENUM.Pants,
            pathIdKey: PATH_ID_ENUM.pantsId,
            inscriptionId: '429d0043926a3f05465130bf13cd1b20b89b347d726e38f5ca485439634263adi0',
            childrenInscription: []
        },
        {
            type: FATHER_TYPE_ENUM.Hat,
            pathIdKey: PATH_ID_ENUM.hatId,
            inscriptionId: '4020db006f708ef58d87f5118e34e5b3a0d54f47525ad27abdaa287fa18291cai0',
            childrenInscription: []
        },
        {
            type: FATHER_TYPE_ENUM.Shoes,
            pathIdKey: PATH_ID_ENUM.shoesId,
            inscriptionId: '8f6d171b30c6b26e7afd334e586015c457327819b2f28280010f5569cfd2aaaci0',
            childrenInscription: []
        },
        {
            type: FATHER_TYPE_ENUM.Shirt,
            pathIdKey: PATH_ID_ENUM.shirtId,
            inscriptionId: '84a564fe1bc2a3beab7cd99cb3dca034ee6889d3f6dcd4143d7ec9d1181e79aai0',
            childrenInscription: [],
            // 支持设置贴图
            texture: {
                pathIdKey: PATH_ID_ENUM.shirtTextureId,//对应IAvatarMetadata的key，用来对应渲染部件
            },
            // 支持设置颜色
            color: {
                pathIdKey: PATH_ID_ENUM.shirtColor,//对应IAvatarMetadata的key，用来对应渲染部件
            }
        },
        {
            type: FATHER_TYPE_ENUM.Pet,//类型名称
            pathIdKey: PATH_ID_ENUM.petId,//对应IAvatarMetadata，用来对应渲染部件
            inscriptionId: "c86687007c50d6462173fc4fd420beca2bb2d62cf116c7546207c675b742d161i0",
            childrenInscription: []
        },
        // {
        //   type: FATHER_TYPE_ENUM.ShirtTexture,
        //   pathIdKey: PATH_ID_ENUM.shirtTextureId,
        //   inscriptionId: '92dc4f05a8e76fdf39fe35b4e3dbfce70c5757ed068184d6ce07a7b8ea6eaf49i0',
        //   childrenInscription: []
        // }
    ] as IFatherInscription[]
}

// 此配置为装饰副配置，例如 贴图 颜色，目的是为了排除渲染avatar时不把副配置当作主装饰使用----后续有新的副配置需要手动添加到这里，否则可能渲染报错
export const EXCLUDE_APPENDIX_PATH_ID = [PATH_ID_ENUM.shirtTextureId, PATH_ID_ENUM.shirtColor]

// 菜单分组
export const MENU_GROUP = [
    {
        title: 'Clothes Group',
        icon: SVG_FILE.shirtSelectIcon,
        unSelectIcon: SVG_FILE.shirtUnselectIcon,
        fathersType: [FATHER_TYPE_ENUM.Shirt, FATHER_TYPE_ENUM.Pants, FATHER_TYPE_ENUM.Shoes, FATHER_TYPE_ENUM.Hat],//匹配CONFIG中的type
    },
    {
        title: 'Hair Group',
        icon: SVG_FILE.kingSelectIcon,
        unSelectIcon: SVG_FILE.kingUnselectIcon,
        fathersType: [],
    },
    {
        title: 'Pet Group',
        icon: SVG_FILE.petSelectIcon,
        unSelectIcon: SVG_FILE.petUnselectIcon,
        fathersType: [FATHER_TYPE_ENUM.Pet],
    },
    {
        title: 'Action Group',
        icon: SVG_FILE.actionSelectIcon,
        unSelectIcon: SVG_FILE.actionUnselectIcon,
        fathersType: [FATHER_TYPE_ENUM.Action],
    },
]

const CONFIG = (() => {
    switch (process.env.USE_CONFIG as string) {
        case 'CHAIN_PRO_CONFIG':
            return CHAIN_PRO_CONFIG
        case 'CHAIN_TEST_CONFIG':
            return CHAIN_TEST_CONFIG
        case 'LOCAL_TEST_CONFIG':
            return LOCAL_TEST_CONFIG
    }
    throw new Error('USE_CONFIG value is not support')
})()

const {AVATAR_INSCRIPTION_ID, DEFAULT_FATHER_INSCRIPTION} = CONFIG
export {ORD_SERVER, CDN_SERVER, CDN_VERSION, AVATAR_INSCRIPTION_ID, DEFAULT_FATHER_INSCRIPTION, loadAvatarModel}
