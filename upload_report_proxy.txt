============================================================
JSON 文件上传报告 (代理模式)
============================================================
上传时间: 2025-07-08 18:47:45
目标存储桶: satworld-resource
代理服务器: http://192.168.30.140:3000/
目标端点: obs.ap-southeast-1.myhuaweicloud.com
连接模式: 通过 Vite 代理服务器

总文件数: 1
成功上传: 1
上传失败: 0

成功上传的文件:
----------------------------------------
✅ ./game_config.json -> website_public/json_files/game_config.json (0.01 MB)

代理配置信息:
----------------------------------------
📡 代理服务器: http://192.168.30.140:3000/
🎯 目标端点: obs.ap-southeast-1.myhuaweicloud.com
📁 存储桶: satworld-resource
🔧 配置文件: vite.config.js

注意事项:
----------------------------------------
✅ 使用与 Node.js 相同的代理配置
🔧 需要 Vite 开发服务器运行 (npm run dev)
📡 通过 /satworld-resource 代理路径访问 OBS

============================================================