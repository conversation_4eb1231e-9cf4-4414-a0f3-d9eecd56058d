<svg width="128" height="101" viewBox="0 0 128 101" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g opacity="0.5">
        <path d="M21.118 44.6646L29.1439 50.7606M22.0831 51.7256L28.1791 43.6997" stroke="url(#paint0_linear_1128_1878)" stroke-width="3.63636" stroke-linecap="round"/>
        <circle cx="105.899" cy="46.4852" r="3.48485" stroke="url(#paint1_linear_1128_1878)" stroke-width="2.72727"/>
        <circle cx="39.8379" cy="31.9391" r="2.87879" stroke="url(#paint2_linear_1128_1878)" stroke-width="1.51515"/>
        <circle cx="74.2324" cy="12.3939" r="1.36364" fill="#CAC8C7"/>
        <circle cx="106.202" cy="65.8785" r="4.54545" fill="#837C73"/>
        <circle cx="28.3233" cy="36.4851" r="2.72727" fill="#837C73"/>
        <circle cx="85.2934" cy="15.5757" r="4.54545" fill="#837C73"/>
        <g filter="url(#filter0_d_1128_1878)">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M90.2017 17.5545C90.0005 16.8287 89.2491 16.4035 88.5234 16.6046C87.7976 16.8058 87.3724 17.5572 87.5735 18.283L88.0373 19.9563L86.3642 20.4201C85.6384 20.6212 85.2132 21.3726 85.4143 22.0984C85.6155 22.8242 86.3669 23.2494 87.0927 23.0483L88.7658 22.5845L89.2297 24.2581C89.4308 24.9838 90.1823 25.4091 90.908 25.2079C91.6338 25.0068 92.059 24.2553 91.8579 23.5296L91.394 21.856L93.0678 21.3921C93.7935 21.1909 94.2188 20.4395 94.0176 19.7138C93.8164 18.988 93.065 18.5627 92.3393 18.7639L90.6655 19.2278L90.2017 17.5545Z" fill="url(#paint3_linear_1128_1878)"/>
        </g>
        <ellipse cx="65.2939" cy="55.5756" rx="33.3333" ry="32.4242" fill="url(#paint4_linear_1128_1878)"/>
        <path d="M32.7329 72.6906C34.9859 71.1934 34.8574 68.7076 34.5116 67.6518C38.2172 61.8932 59.2653 66.932 66.2319 67.2199C73.1985 67.5079 73.7914 64.6285 81.3509 62.757C88.9104 60.8854 96.9146 64.4846 96.4699 67.6518C96.1141 70.1856 97.4086 71.5869 98.1004 71.9708C99.3356 72.7866 101.628 75.0805 100.917 77.7294C100.027 81.0406 94.0983 81.9044 95.1359 82.4803C96.1734 83.0562 97.9521 85.0717 96.4699 87.6631C94.9876 90.2545 88.6265 89.8179 86.9835 89.5346C86.1381 89.3889 78.2382 87.6631 73.7914 88.6708C69.3446 89.6786 57.3384 89.8226 52.7434 87.6631C48.1484 85.5036 43.1087 89.3907 39.996 86.5113C37.5058 84.2079 40.3418 81.8085 42.0711 80.8967C39.9466 80.8487 34.9563 80.4072 31.9918 79.0251C28.2861 77.2975 29.9166 74.5622 32.7329 72.6906Z" fill="#CFCFCF"/>
        <path opacity="0.6" d="M93.4746 70.986C82.8371 84.3492 48.1716 72.4445 34.8383 69.0607C24.7777 44.2122 51.808 28.4042 66.6565 30.1213C93.4746 33.2228 104.535 57.0911 93.4746 70.986Z" fill="url(#paint5_linear_1128_1878)"/>
        <g filter="url(#filter1_d_1128_1878)">
            <path d="M54.1575 63.1514C54.7916 67.592 61.5793 76.5354 72.5725 74.1891" stroke="#837C73" stroke-width="4.84848" stroke-linecap="round"/>
        </g>
        <g filter="url(#filter2_d_1128_1878)">
            <path d="M55.7247 39.9351L58.9882 51.7088" stroke="#837C73" stroke-width="4.84848" stroke-linecap="round"/>
            <path d="M51.4706 47.4536L63.2444 44.1902" stroke="#837C73" stroke-width="4.84848" stroke-linecap="round"/>
        </g>
        <g filter="url(#filter3_d_1128_1878)">
            <path d="M79.9661 57.2075L83.2295 68.9813" stroke="#837C73" stroke-width="4.84848" stroke-linecap="round"/>
            <path d="M75.7119 64.7261L87.4857 61.4626" stroke="#837C73" stroke-width="4.84848" stroke-linecap="round"/>
        </g>
        <path d="M43.1725 78.3025C35.7786 79.5146 31.4049 76.9893 30.1422 75.5752C28.1725 79.9691 39.3846 80.7267 42.1119 80.8782C44.8392 81.0297 51.3543 76.9612 43.1725 78.3025Z" fill="url(#paint6_linear_1128_1878)"/>
        <path opacity="0.7" d="M49.0809 84.818C43.99 87.2423 40.394 85.8281 39.2324 84.818C39.2324 86.9393 43.0203 88.6059 46.0506 87.3938C48.4748 86.4241 51.2021 86.9897 52.2627 87.3938C59.2324 91.4847 74.687 88.6059 76.9597 88.3029C79.2324 87.9998 85.293 89.9695 90.8991 89.9695C95.8991 89.9695 98.0203 84.818 96.5051 86.4847C94.99 88.1513 88.7779 89.0604 84.2324 87.3938C80.5961 86.0604 77.8688 86.2321 76.9597 86.4847C66.6567 88.9089 56.9092 86.8887 53.3233 85.5756C51.6264 84.3635 49.788 84.5655 49.0809 84.818Z" fill="url(#paint7_linear_1128_1878)"/>
        <path d="M93.9295 81.3328C92.5962 81.8176 93.98 82.2419 94.8386 82.3934C94.5356 82.3157 95.0166 82.5897 97.869 80.8782C101.657 78.6055 101.101 76.9155 100.748 75.5752C100.899 78.3025 95.5962 80.7267 93.9295 81.3328Z" fill="url(#paint8_linear_1128_1878)"/>
    </g>
    <defs>
        <filter id="filter0_d_1128_1878" x="84.1524" y="16.5547" width="11.1272" height="11.1274" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.21212"/>
            <feGaussianBlur stdDeviation="0.606061"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1128_1878"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1128_1878" result="shape"/>
        </filter>
        <filter id="filter1_d_1128_1878" x="51.7329" y="60.7271" width="25.6885" height="19.9088" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dx="1.21212" dy="2.42424"/>
            <feGaussianBlur stdDeviation="0.606061"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1128_1878"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1128_1878" result="shape"/>
        </filter>
        <filter id="filter2_d_1128_1878" x="47.8337" y="37.5103" width="19.0477" height="19.0478" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.21212"/>
            <feGaussianBlur stdDeviation="0.606061"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1128_1878"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1128_1878" result="shape"/>
        </filter>
        <filter id="filter3_d_1128_1878" x="72.075" y="54.7827" width="19.0477" height="19.0478" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.21212"/>
            <feGaussianBlur stdDeviation="0.606061"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1128_1878"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1128_1878" result="shape"/>
        </filter>
        <linearGradient id="paint0_linear_1128_1878" x1="21.884" y1="51.5739" x2="27.9801" y2="43.548" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FF8A00"/>
            <stop offset="1" stop-color="#FFB06A"/>
        </linearGradient>
        <linearGradient id="paint1_linear_1128_1878" x1="105.899" y1="41.6367" x2="105.899" y2="51.3337" gradientUnits="userSpaceOnUse">
            <stop stop-color="#F9B34C"/>
            <stop offset="1" stop-color="#FF7E20"/>
        </linearGradient>
        <linearGradient id="paint2_linear_1128_1878" x1="39.8379" y1="28.3027" x2="39.8379" y2="35.5755" gradientUnits="userSpaceOnUse">
            <stop stop-color="#F9B34C"/>
            <stop offset="1" stop-color="#FF8F3E"/>
        </linearGradient>
        <linearGradient id="paint3_linear_1128_1878" x1="88.5237" y1="16.6046" x2="90.9083" y2="25.2078" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FF8A00"/>
            <stop offset="1" stop-color="#FF9142"/>
        </linearGradient>
        <linearGradient id="paint4_linear_1128_1878" x1="65.2939" y1="23.1514" x2="74.8895" y2="72.5453" gradientUnits="userSpaceOnUse">
            <stop stop-color="#ECECEC"/>
            <stop offset="0.684045" stop-color="#CECECE"/>
        </linearGradient>
        <linearGradient id="paint5_linear_1128_1878" x1="65.1104" y1="44.6668" x2="65.1104" y2="74.9455" gradientUnits="userSpaceOnUse">
            <stop stop-color="#F3F3F3" stop-opacity="0"/>
            <stop offset="1" stop-color="#F3F3F3"/>
        </linearGradient>
        <linearGradient id="paint6_linear_1128_1878" x1="38.5137" y1="77.2254" x2="38.5137" y2="80.6308" gradientUnits="userSpaceOnUse">
            <stop stop-color="#989898" stop-opacity="0"/>
            <stop offset="1" stop-color="#A5A5A5"/>
        </linearGradient>
        <linearGradient id="paint7_linear_1128_1878" x1="61.8082" y1="85.8786" x2="97.1112" y2="86.1816" gradientUnits="userSpaceOnUse">
            <stop stop-color="#AEAEAE"/>
            <stop offset="0.832676" stop-color="#818181" stop-opacity="0.553704"/>
            <stop offset="1" stop-color="#F3F3F3" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="paint8_linear_1128_1878" x1="97.1789" y1="77.6953" x2="97.1789" y2="82.0702" gradientUnits="userSpaceOnUse">
            <stop stop-color="#989898" stop-opacity="0"/>
            <stop offset="1" stop-color="#A5A5A5"/>
        </linearGradient>
    </defs>
</svg>
