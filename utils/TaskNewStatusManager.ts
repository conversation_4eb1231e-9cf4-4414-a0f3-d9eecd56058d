type TaskNewStatus = {
  [taskType: string]: {
    [taskId: string]: boolean;
  };
};

const LOCAL_STORAGE_KEY = "task_new_status";

export class TaskNewStatusManager {
  private status: TaskNewStatus = {}; // 任务新状态
  private lastViewedTaskType: string | null = null; // 最后查看的任务类型

  constructor() {
    this.load();
  }

  // 本地存储获取状态
  private load() {
    if (typeof window !== "undefined") {
      try {
        const data = localStorage.getItem(LOCAL_STORAGE_KEY);
        this.status = data ? JSON.parse(data) : {};
      } catch (error) {
        this.status = {};
      }
    }
  }

  // 持久化
  private save() {
    if (typeof window === "undefined") return;
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(this.status));
  }

  // 获取存储在浏览器本地任务新状态
  getTaskNewStatus() {
    return this.status;
  }

  // 同步任务列表，自动标记新item
  /**
   * 同步任务列表，自动标记新item
   * @param taskType 任务类型
   * @param taskItems 任务列表
   * 新 id → 标记为 new
   * 本地有但服务端没有的 id → 移除
   * 两边都有的 id → 保持原状态
   */
  syncTaskItems(taskType: string, taskItems: { id: string }[]) {
    // console.log("taskType", taskType);
    // console.log("taskItems", taskItems);
    // console.log("this.status", this.status);
    if (!this.status[taskType]) this.status[taskType] = {};

    // 标记新item
    taskItems.forEach((item) => {
      if (!(item.id in this.status[taskType])) {
        this.status[taskType][item.id] = true;
      }
    });

    // 清理已不存在的item
    Object.keys(this.status[taskType]).forEach((itemId) => {
      if (!taskItems.find((item) => item.id === itemId)) {
        delete this.status[taskType][itemId];
      }
    });

    this.save();
  }

  reload() {
    this.load()
  }

  // 标记某个任务为已阅
  markTaskAsRead(taskType: string, taskId: string) {
    if (this.status[taskType] && this.status[taskType][taskId]) {
      this.status[taskType][taskId] = false;
      this.save();
    }
  }

  hasNewInTaskType(taskType: string): boolean {
    if (!this.status[taskType]) return false;
    return Object.values(this.status[taskType]).some(Boolean);
  }

  setLastViewedTaskType(taskType: string) {
    this.lastViewedTaskType = taskType;
  }

  // 切换tab时，清楚上一个taskType的new
  onTaskChange(newTaskType: string) {
    if (this.lastViewedTaskType && this.status[this.lastViewedTaskType]) {
      Object.keys(this.status[this.lastViewedTaskType]).forEach((itemId) => {
        this.lastViewedTaskType &&
          (this.status[this.lastViewedTaskType][itemId] = false);
      });
      this.save();
    }

    this.lastViewedTaskType = newTaskType;
  }
  
  // 判断某个item是否new
  isTaskItemNew(taskType: string, itemId: string): boolean {
    return !!(this.status[taskType] && this.status[taskType][itemId]);
  }

  // 全部标记为已阅
  markAllAsRead() {
    Object.keys(this.status).forEach((taskType) => {
      Object.keys(this.status[taskType]).forEach((itemId) => {
        this.status[taskType][itemId] = false;
      });
    });
    this.save();
  }

  // 重置
  reset() {
    this.status = {};
    this.save();
  }

  // 检查所有类型下是否有 new 状态的任务
  hasAnyNewTask(): boolean {
    return Object.values(this.status).some(
      (typeObj) => Object.values(typeObj).some(Boolean)
    );
  }
}
