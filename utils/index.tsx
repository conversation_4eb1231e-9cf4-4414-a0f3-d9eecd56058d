import {IS_LOCAL_TEST, ORD_NFT_IMG_SERVER, ORD_SERVER, CDN_SERVER, CDN_VERSION, IS_TEST_ENV} from "../constant";
import CBOR from "cbor";

export const getCdnLink = (path: string, force = false) => {
    if (CDN_SERVER.length === 0 || path.includes(CDN_SERVER)) {
        return path
    }

    path = path.replace('./', '/')
    if (IS_TEST_ENV || force) {
        const timestamp = new Date().getTime() // 获取当前时间戳
        return `${CDN_SERVER}/website_public/${CDN_VERSION}${path}?t=${timestamp}`
    } else {
        return `${CDN_SERVER}/website_public/${CDN_VERSION}${path}`
    }
}

export const getOrdLink = (ordId: string) => {
    if (IS_LOCAL_TEST) {
        return {
            preview: ordId,
            content: ordId
        }
    }
    return {
        preview: `${ORD_SERVER}/preview/${ordId}`,//预览
        content: `${ORD_SERVER}/content/${ordId}`,//内容
    }
}
export const getOrdInscriptionLink = (inscription: string) => {
    return `${ORD_SERVER}/inscription/${inscription}`
}

const loadFile = (url: string) => {
    return new Promise((resolve, reject) => {
        fetch(url).then(() => {
            resolve(true)
        }).catch(() => {
            reject(false)
        })
    })
}

export const loadFiles = (urls: string[]) => {
    const promiseList = []
    for (let i = 0; i < urls.length; i++) {
        const url = urls[i];
        promiseList.push(loadFile(url))
    }
    return Promise.all(promiseList)
}

export function toFormatStr(str: string, left: number = 6, right: number = 6) {
    if (!str) {
        return ''
    }
    return str.slice(0, left) + '...' + str.slice((str.length - right), str.length)
}

export const getLocationParams = (key: string) => {
    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);
    return params.get(key);
}
export const checkIsBtcAddress = (address: string) => {
    return /^bc1[a-zA-HJ-NP-Z0-9]{39,59}$/.test(address)
}

export const checkIsInscriptionId = (id: string) => {
    if (id.length === 66 && id.endsWith("i0")) {
        return true
    }
    return false
}

function hexToUint8Array(hex: string) {
    let bytes = new Uint8Array(hex.length / 2);
    for (let i = 0; i < hex.length; i += 2) {
        bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
    }
    return bytes;
}

export const decodeFetchResponse = async (resp: any) => {
    let metadata = await resp.text();
    metadata = metadata.replace(/"/g, '');
    const CBOR = require('cbor');
    // 解码metadata
    return CBOR.decode(hexToUint8Array(metadata).buffer);
}
export const getNFTImgLink = (inscriptionId: string) => {
    return `${ORD_NFT_IMG_SERVER || ORD_SERVER}/content/${inscriptionId}`
}
