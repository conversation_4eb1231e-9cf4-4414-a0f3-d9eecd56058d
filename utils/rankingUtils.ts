/**
 * 计算要显示的总积分
 * 优先使用 userScore.totalScore，如果不可用则使用 selfRankInfo.score
 *
 * @param userScore 用户积分信息（来自砍树操作）
 * @param selfRankInfo 用户排名信息（来自排行榜接口）
 * @returns 显示的总积分
 */
export const calculateDisplayScore = (
  userScore: any | null | undefined,
  selfRankInfo: any | null | undefined
): number => {
  if (userScore?.totalScore !== undefined) {
    return userScore.totalScore;
  }
  return selfRankInfo?.score || 0;
};

/**
 * 计算要显示的排名
 * 优先使用 userScore.rank，如果不可用则使用 selfRankInfo.rank
 *
 * @param userScore 用户积分信息（来自砍树操作）
 * @param selfRankInfo 用户排名信息（来自排行榜接口）
 * @returns 显示的排名
 */
export const calculateDisplayRank = (
  userScore: any | null | undefined,
  selfRankInfo: any | null | undefined
): string | number => {
  // 如果 userScore 中有 rank 且不为 undefined 或 null，优先使用它
  if (userScore?.rank !== undefined && userScore?.rank !== null) {
    return userScore.rank;
  }
  // 否则使用 selfRankInfo 中的 rank，如果也没有则显示 "-"
  return selfRankInfo?.rank || "-";
};
