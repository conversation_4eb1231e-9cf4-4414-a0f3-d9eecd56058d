import type { AppProps } from "next/app";
import store from "../store";
import { Provider } from "react-redux";
import { Toaster } from "react-hot-toast";
import ConnectWalletModal from "../components/Basic/ConnectWalletModal";
import GlobalStyleProvider from "../components/GlobalStyleProvider";
import useConnectWallet from "../hooks/useConnectWallet";
import useDomainOwner from "../hooks/useDomainOwner";
import useTtsWhiteList from "../hooks/useTtsWhiteList";
import useJump from "../hooks/useJump";
import { IS_TEST_ENV } from "@/AvatarOrdinalsBrowser/constant";
import { TaskProvider } from "contexts/TaskContext";
import TaskUpdateListener from "../components/TaskUpdateListener";
import "rc-tooltip/assets/bootstrap.css";
import React from "react";

function GlobalHook() {
  useConnectWallet(true); // 处理钱包连接相关的逻辑
  useDomainOwner(); // 处理域名所有权相关的逻辑
  useTtsWhiteList(); // 处理白名单相关的逻辑
  useJump(); // 处理页面跳转相关的逻辑
  return null; // 不渲染任何 UI
}

export default function App({ Component, pageProps }: AppProps) {
  if (typeof window !== "undefined" && !IS_TEST_ENV) {
    // import("disable-devtool").then(({ default: disableDevtool }) => {
    //   disableDevtool({
    //     // 禁用行为
    //     disableMenu: true, // 禁用右键菜单
    //     disableSelect: true, // 禁用文本选择
    //     disableCopy: true, // 禁用复制
    //     disableCut: true, // 禁用剪切
    //     disablePaste: true, // 禁用粘贴
    //     // 检测控制
    //     clearLog: true, // 每次检测都清除console日志
    //     clearIntervalWhenDevOpenTrigger: false, // 触发后是否停止监控
    //     stopIntervalTime: 5000, // 移动端取消监视的等待时长
    //     disableIframeParents: true, // iframe中禁用所有父窗口
    //     tkName: "s1a2", // 自定义tkName
    //     md5: "e807f1fcf82d132f9bb018ca6738a19f", // 自定义md5
    //   });
    // });
  }

  return (
    <Provider store={store}>
      <GlobalStyleProvider>
        <TaskProvider>
          <Component {...pageProps} />
          <Toaster />
          <ConnectWalletModal />
          <GlobalHook />
          <TaskUpdateListener />
        </TaskProvider>
      </GlobalStyleProvider>
    </Provider>
  );
}
