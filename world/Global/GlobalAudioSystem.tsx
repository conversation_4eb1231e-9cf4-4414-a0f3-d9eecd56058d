import * as THREE from "three";
import {useEffect, useRef, useState} from "react";
import GlobalSpace from "./GlobalSpace";
import {AudioListener} from "three/src/audio/AudioListener";
import {useThree} from "@react-three/fiber";
import {LoaderUtil} from "@/world/Util/LoaderUtil";

class GlobalAudioSystem {
  playMap: Map<string, (url: string, callback: () => boolean) => void> = new Map()

  constructor() {
  }

  playAudio(key: string, url: string, callback: () => boolean) {
    const changeUrl = this.playMap.get(key)
    if (changeUrl) {
      changeUrl(url, callback);
    }
  }
}

export const AudioSystem = new GlobalAudioSystem()

export default function AudioSystemComponent({_key}: { _key: string }) {
  const {camera} = useThree()

  const groupRef = useRef<THREE.Group>(null)

  const audioRef = useRef<any>(null)
  const effectVolume = 1.5;
  const [distance, setDistance] = useState<number>(9999);
  const [realVolume, setRealVolume] = useState<number>(0);

  useEffect(() => {
    const cancelPositionKey = GlobalSpace.whatCharacterPosition((position) => {
      if (groupRef.current) {
        const worldPos = groupRef.current.getWorldPosition(new THREE.Vector3());
        setDistance(position.distanceTo(worldPos));
      }
    })
    return () => {
      GlobalSpace.cancelCharacterPositionCallback(cancelPositionKey)
    };
  }, []);

  useEffect(() => {
    const maxDistance = 9;
    const minDistance = 1;
    const maxVolume = 2;
    const minVolume = 0;

    const _realVolume = Math.max(0, (maxVolume - minVolume) * (1 - (distance - minDistance) / (maxDistance - minDistance)) + minVolume)
    setRealVolume(_realVolume);
    const audio = audioRef.current as THREE.Audio
    if (audio && audio.isPlaying) {
      audio.setVolume(_realVolume * effectVolume);
    }
  }, [distance, effectVolume]);

  const changeUrl = (url: string, callback: () => boolean) => {
    // 创建音频监听器
    let listener = camera.userData.audioListener as AudioListener;
    if (!listener) {
      listener = new AudioListener();
      camera.userData.audioListener = listener;
      camera.add(listener)
    }

    const soundEffectsSwitch = localStorage.getItem('soundEffects') === 'true';
    if (!soundEffectsSwitch) {
      return;
    }
    if (listener) {
      if (!audioRef.current) {
        // 创建 Audio 对象
        audioRef.current = new THREE.Audio(listener);
      }
      const audio = audioRef.current as THREE.Audio
      if (audio) {
        if (url.length === 0) {
          audio.stop();
          return
        }
        const playBuffer = (buffer: AudioBuffer) => {
          if (audio.isPlaying) {
            audio.stop();
          }
          audio.setBuffer(buffer);
          audio.setLoop(false);
          audio.setVolume(realVolume * effectVolume);
          if (callback()) {
            audio.play();
          }
        }
        LoaderUtil.loadAudio(url, (buffer) => {
          playBuffer(buffer); // playBuffer
        });
      }
    }
  }

  AudioSystem.playMap.set(_key, changeUrl)
  return (
    <group ref={groupRef}>
    </group>
  )
}

