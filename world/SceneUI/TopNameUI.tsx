import {Html} from "@react-three/drei";
import React from "react";
import * as THREE from "three";
import {useFrame} from "@react-three/fiber";

import styled from "styled-components";

const UIBox = styled.div`
    .bubble-container {
        position: relative;
        display: inline-block;
        bottom: 0;
        pointer-events: none; /* 使鼠标事件穿透 */
    }

    //

    .bubble-text {
        position: relative; /* 改为相对定位，方便独立展示 */
        max-width: 900px; /* 固定宽度 */
        color: #ffffff;
        font-size: 35px;
        word-wrap: break-word; /* 允许长单词换行 */
        white-space: normal; /* 多行文字正常换行 */
        user-select: none;
        pointer-events: none; /* 使鼠标事件穿透 */
    }
`;

export default function TopNameUI({
                                    height,
                                    name,
                                  }: {
  height: number;
  name: string;
}) {
  const ref = React.useRef<THREE.Group>(null);
  // 在每一帧更新时，让元素朝向摄像机
  useFrame(({camera}) => {
    if (ref.current) {
      // 只计算水平方向的朝向
      const cameraWorldPos = new THREE.Vector3();
      camera.getWorldPosition(cameraWorldPos);
      const objWorldPos = new THREE.Vector3();
      ref.current.getWorldPosition(objWorldPos);
      cameraWorldPos.y = objWorldPos.y;
      ref.current.lookAt(cameraWorldPos);
    }
  });
  return (
    <group ref={ref} position={[0, height, 0]}>
      {name.length > 0 && (
        <Html
          distanceFactor={1} // 让它始终保持固定的大小，不随摄像机远近变化
          transform // 保证HTML内容的大小不随距离变化
          // occlude  // 确保 HTML 元素不会被 3D 场景中的物体遮挡
          pointerEvents="none" // 禁用鼠标事件
          position={[0, 0, 0]}
          style={{
            transformOrigin: "bottom center",
            transform: "translate(0,-50%)",
            transition: "all 0.3s ease",
            left: "50%",
          }}
          center={false}
        >
          <UIBox>
            <div className="bubble-container">
              <div className="bubble-text">
                {name}
              </div>
            </div>
          </UIBox>
        </Html>
      )}
    </group>
  );
}
