/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three'
//@ts-ignore
// import * as WebGpg from 'three/webgpu';
// import * as WebGpg from 'three/src/Three.WebGPU';
// import {
//   color,
//   linearDepth,
//   mx_worley_noise_float,
//   positionWorld,
//   screenUV,
//   time,
//   vec2,
//   viewportDepthTexture,
//   viewportLinearDepth,
//   viewportSharedTexture
// } from 'three/tsl';
// } from 'three/src/nodes/TSL';
import {useEffect, useRef, useState} from "react";
import {useControls} from "leva";
import {IS_AVATAR_PAGE} from "../../../AvatarOrdinalsBrowser/constant";
import {FishConfig} from "@/world/Config/FishConfig";
import {useFrame} from "@react-three/fiber";

export default function Island_Water({
                                       mesh,
                                       color1 = '#488ae4',
                                       color2 = '#51b6e0',
                                       depthNum1 = 0.5,
                                       depthNum2 = 0.6,
                                       waveSpeed = 0.3,
                                       waveNum1 = 0.5,
                                       waveNum2 = 1.5,
                                       floatingNum = 0.3,
                                     }: {
  mesh: THREE.Mesh,
  color1?: string,
  color2?: string,
  waveSpeed?: number,
  depthNum1?: number,
  depthNum2?: number,
  waveNum1?: number,
  waveNum2?: number,
  floatingNum?: number,
}) {
  const groupRef = useRef<THREE.Group>(null);
  const [lowQuality, setLowQuality] = useState<boolean>(false);
  let waterDebug = null;
  if (IS_AVATAR_PAGE) {
    // Character Controls
    waterDebug = useControls(
      "Water Controls",
      {
        color1: {
          value: color1,
        },
        color2: {
          value: color2,
        },
        waveSpeed: {
          value: waveSpeed,
          min: 0,
          max: 2,
          step: 0.1,
        },
        depthNum1: {
          value: depthNum1,
          min: 0,
          max: 5,
          step: 0.1,
        },
        depthNum2: {
          value: depthNum2,
          min: 0,
          max: 1,
          step: 0.01,
        },
        waveNum1: {
          value: waveNum1,
          min: 0,
          max: 10,
          step: 0.1,
        },
        waveNum2: {
          value: waveNum2,
          min: 0,
          max: 10,
          step: 0.1,
        },
        floatingNum: {
          value: floatingNum,
          min: 0,
          max: 1,
          step: 0.1,
        }
      }
    );
    // Apply debug values
    color1 = waterDebug.color1;
    color2 = waterDebug.color2;
    waveSpeed = waterDebug.waveSpeed;
    depthNum1 = waterDebug.depthNum1;
    depthNum2 = waterDebug.depthNum2;
    waveNum1 = waterDebug.waveNum1;
    waveNum2 = waterDebug.waveNum2;
    floatingNum = waterDebug.floatingNum;
  }

  useFrame(() => {
    if (groupRef.current) {
      const specialEffects = localStorage.getItem('specialEffects') === 'true';
      setLowQuality(!specialEffects)
    }
  });

  useEffect(() => {
    if (groupRef.current) {
      // if (lowQuality) {
        const water = new THREE.Mesh(mesh.geometry, mesh.material);
        water.position.copy(mesh.position);
        water.scale.copy(mesh.scale);
        groupRef.current.add(water);
        FishConfig.getInstance().saveWaterMesh(water);

        return () => {
          groupRef.current?.remove(water);
        }
      // } else {
      //
      //   const timer = time.mul(waveSpeed);
      //   const floorUV = positionWorld.xzy;
      //
      //   const waterLayer0 = mx_worley_noise_float(floorUV.mul(waveNum1).add(timer));
      //   const waterLayer1 = mx_worley_noise_float(floorUV.mul(waveNum2).add(timer));
      //
      //   const waterIntensity = waterLayer0.mul(waterLayer1);
      //   const waterColor = waterIntensity.mul(1.4).mix(color(color1), color(color2));
      //
      //   // linearDepth() returns the linear depth of the mesh
      //   const depth = linearDepth();
      //   const depthWater = viewportLinearDepth.sub(depth);
      //   const depthEffect = depthWater.remapClamp(-.002, .04).mul(depthNum1).add(depthNum2);
      //
      //   const refractionUV = screenUV.add(vec2(0, waterIntensity.mul(.1)));
      //
      //   // linearDepth( viewportDepthTexture( uv ) ) return the linear depth of the scene
      //   const depthTestForRefraction = linearDepth(viewportDepthTexture(refractionUV)).sub(depth);
      //
      //   const depthRefraction = depthTestForRefraction.remapClamp(0, .1).mul(depthNum1).add(depthNum2);
      //
      //   const finalUV = depthTestForRefraction.lessThan(0).select(screenUV, refractionUV);
      //
      //   const viewportTexture = viewportSharedTexture(finalUV);
      //
      //   ///漂浮物计算
      //   const floatingEffect = depthTestForRefraction.lessThan(floatingNum / 1000).select(1, 0);
      //   // const floatingEffect = depthWater.remapClamp(-.002, .04).lessThan(0.05).select(1, 0);
      //
      //   const waterMaterial = new WebGpg.MeshBasicNodeMaterial();
      //   waterMaterial.colorNode = waterColor;
      //   // waterMaterial.backdropNode = depthEffect; //深度图
      //   // waterMaterial.backdropNode = viewportSharedTexture();//水底图
      //   // waterMaterial.backdropNode = viewportTexture;//折射后的水底图
      //   // waterMaterial.backdropNode = depthRefraction;//折射后的深度图
      //   // waterMaterial.backdropNode = depthRefraction.mul(0.5).add(0.3);//折射后的深度图 设置返回 0.3-0.8
      //   waterMaterial.backdropNode = depthEffect.mix(viewportSharedTexture(), viewportTexture.mul(depthRefraction.mix(1, waterColor))).add(floatingEffect);
      //   waterMaterial.backdropAlphaNode = depthRefraction.oneMinus();
      //   waterMaterial.transparent = true;
      //
      //   const water = new THREE.Mesh(mesh.geometry, waterMaterial);
      //   water.position.copy(mesh.position);
      //   water.scale.copy(mesh.scale);
      //   groupRef.current.add(water);
      //   FishConfig.getInstance().saveWaterMesh(water);
      //   return () => {
      //     groupRef.current?.remove(water);
      //   }
      // }
    }
  }, [color1, color2, depthNum1, depthNum2, waveNum1, waveNum2, floatingNum, waveSpeed, lowQuality]);
  return (
    <>
      <group ref={groupRef}/>
    </>
  )
}
