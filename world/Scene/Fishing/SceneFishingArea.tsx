import React, {useEffect, useRef, useState} from 'react'
import {FishingAreaConfig, FishingAreaData} from "@/world/Config/FishingAreaConfig";
import {useFrame, useThree} from "@react-three/fiber";
import {ItemType} from "@/world/Config/ItemConfig";
import * as THREE from "three";
import {Quaternion} from "three";
import {AppGameApiKey, GetMyPlayer} from "@/world/Character/MyPlayer";
import GlobalSpace, {GAME_OP_TYPE} from "@/world/Global/GlobalSpace";
import {UseGameState} from "@/src/stores/useGame";
import {FishData} from "@/world/Config/FishConfig";
import {GLTF} from "three-stdlib";
import {LoaderUtil} from "@/world/Util/LoaderUtil";
import SceneFishFloat from "@/world/Scene/Fishing/SceneFishFloat";
import SceneFish from "@/world/Scene/Fishing/SceneFish";
import AudioSystemComponent, {AudioSystem} from "@/world/Global/GlobalAudioSystem";
import {getParticleSystem} from "@/world/Particles/ParticleSystem";

import FishingSvg from '@/public/image/gameOpIcon/fishing.svg'
import {KeyPressUtil} from "@/world/Global/GlobalKeyPressUtil";

export default function SceneFishingArea({areaId}: {
  areaId: number
}) {
  const {scene} = useThree()
  const particleSystem = getParticleSystem()
  const floatRef = useRef<THREE.Group>(null)
  const myPlayer = GetMyPlayer()
  const useGame = myPlayer.getUseGame()
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);
  const initializeAnimationSet = useGame(
    (state: UseGameState) => state.initializeAnimationSet
  );
  const playerAction = useGame((state: UseGameState) => state.curAnimation)
  const [areaData, setAreaData] = useState<FishingAreaData | null>(null)
  const [showFishingButton, setShowFishingButton] = useState<boolean>(false)
  const [isFishing, setIsFishing] = useState<boolean>(false)
  const [actionWaiting, setActionWaiting] = useState<boolean>(false)
  const [fishData, setFishData] = useState<FishData | null>(null)
  const fishInitPos = useRef<THREE.Vector3>(new THREE.Vector3())
  const [fishTargetPos, setFishTargetPos] = useState<THREE.Vector3 | null>(null)
  const [fishFacePos, setFishFacePos] = useState<THREE.Vector3>(new THREE.Vector3())
  const [showFish, setShowFish] = useState<boolean>(false)
  const [fishGlb, setFishGlb] = useState<GLTF | null>(null)
  useEffect(() => {
    FishingAreaConfig.getInstance().getData(areaId, (data) => {
      setAreaData(data)
      FishingAreaConfig.getInstance().showAreaBox(scene, data)
    })
  }, []);

  useEffect(() => {

    if (isFishing) {
      const data = myPlayer.itemData
      initializeAnimationSet({
        idle: "Action_17",
        walk: data && data.action_walk.length > 0 ? data.action_walk : "walk",
        run: data && data.action_run.length > 0 ? data.action_run : "run",
        jump: "jump_01",
        jumpIdle: "jump_02",
        jumpLand: "jump_03",
        fall: "fall", // This is for falling from high sky
      }, true)
    } else {
      const data = myPlayer.itemData
      initializeAnimationSet({
        idle: data && data.action_idle.length > 0 ? data.action_idle : "idle",
        walk: data && data.action_walk.length > 0 ? data.action_walk : "walk",
        run: data && data.action_run.length > 0 ? data.action_run : "run",
        jump: "jump_01",
        jumpIdle: "jump_02",
        jumpLand: "jump_03",
        fall: "fall", // This is for falling from high sky
      }, true)
      //条鱼停止后清空数据
      setFishData(null)
    }
  }, [isFishing]);

  useEffect(() => {
    if (fishData) {
      LoaderUtil.loadGlb(fishData.glb_url, (glb) => {
        glb.scene.scale.set(fishData.fish_scale, fishData.fish_scale, fishData.fish_scale)
        setFishGlb(glb)
      })
    }
  }, [fishData]);

  useEffect(() => {
    if (myPlayer.UsePetInscriptionId.length > 0) {
      return
    }

    if (showFishingButton && areaData && !actionWaiting) {
      const opKeyList: string[] = []
      if (isFishing) {
        opKeyList.push(GlobalSpace.addGameOp(GAME_OP_TYPE.CustomOp, () => {
          myPlayer.stopFishing((success, fishData) => {
            setActionWaiting(true)
            if (success) {
              setCurAnimation('Action_18')
              AudioSystem.playAudio('scene_fish_float_' + areaId, './sound/fish/fish_struggle.mp3', () => {
                return true
              })
              setTimeout(() => {
                setCurAnimation('Action_19')
                setTimeout(() => {
                  const sound_url = fishData && (fishData.id == 3001 || fishData.id == 3002) ? './sound/fish/fish_bite_big.mp3' : './sound/fish/fish_bite_small.mp3'
                  AudioSystem.playAudio('scene_fish_float_' + areaId, sound_url, () => {
                    return true
                  })
                }, 0)
                setTimeout(() => {
                  myPlayer.faceCamera()
                }, 600)
                setIsFishing(false)

                if (myPlayer.camera) {
                  //计算玩家手接鱼的位置
                  const tempObject = new THREE.Object3D()
                  tempObject.position.copy(myPlayer.position)
                  tempObject.lookAt(new THREE.Vector3(myPlayer.camera.position.x, myPlayer.position.y, myPlayer.camera.position.z))
                  const tempObject2 = new THREE.Object3D()
                  tempObject2.position.set(0, 0.75, 0.3)
                  tempObject.add(tempObject2)
                  const tempObject3 = new THREE.Object3D()
                  tempObject3.position.set(1, 0.75, 0.3)
                  tempObject.add(tempObject3)
                  const targetPos = new THREE.Vector3()
                  const facePos = new THREE.Vector3()
                  tempObject2.getWorldPosition(targetPos)
                  tempObject3.getWorldPosition(facePos)
                  if (fishData) {
                    targetPos.y += fishData.fish_yOffset
                    facePos.y += fishData.fish_yOffset
                  }
                  setFishTargetPos(targetPos)
                  setFishFacePos(facePos)
                  setTimeout(() => {
                    setFishTargetPos(null)
                    KeyPressUtil.setEnable(true);
                    myPlayer.callAppApi(AppGameApiKey.fishingSuccess, 0)
                    setTimeout(() => {
                      setActionWaiting(false)
                    }, 2000)
                  }, 3000)
                }
              }, 2000)
            } else {
              setCurAnimation('Action_20')
              AudioSystem.playAudio('myPlayer', './sound/rod/rod_reel.mp3', () => {
                return true
              })
              particleSystem.addParticle(fishInitPos.current.clone(), new Quaternion(), './particles/Effect_water_splash.json', 1, 3000)
              setIsFishing(false)
              KeyPressUtil.setEnable(true);
              setTimeout(() => {
                setActionWaiting(false)
              }, 2500)
            }
          })
        }, areaData.id, 'Reel', FishingSvg.src))
      } else {
        opKeyList.push(GlobalSpace.addGameOp(GAME_OP_TYPE.CustomOp, () => {
          KeyPressUtil.setEnable(false);
          setFishTargetPos(null)
          myPlayer.startFishing(
            (fishPos) => {
              fishInitPos.current.copy(fishPos)
              if (floatRef.current) {
                floatRef.current.position.copy(fishPos)
                GlobalSpace.updateCharacterPosition(myPlayer.position)
              }
              setIsFishing(true)
              setActionWaiting(true)
              setTimeout(() => {
                setActionWaiting(false)
              }, 3000)
              setCurAnimation('Action_16')
              setTimeout(() => {
                AudioSystem.playAudio('myPlayer', './sound/rod/rod_cast.mp3', () => {
                  return true
                })
              }, 900)
              setTimeout(() => {
                AudioSystem.playAudio('scene_fish_float_' + areaId, './sound/rod/rod_float.mp3', () => {
                  return true
                })
              }, 1400)
            },
            (fishData) => {
              setFishData(fishData)
            }, () => {
              setCurAnimation('Action_20')
              setIsFishing(false)
              particleSystem.addParticle(fishInitPos.current.clone(), new Quaternion(), './particles/Effect_water_splash.json', 1, 3000)
              KeyPressUtil.setEnable(true);
            }, areaData)
        }, areaData.id, 'Cast', FishingSvg.src))
      }


      return () => {
        opKeyList.forEach((key) => {
          GlobalSpace.removeGameOp(key)
        })
      }
    }

  }, [showFishingButton, areaData, isFishing, actionWaiting]);

  useEffect(() => {
    if (playerAction === 'Action_16') {
      //甩竿后 1000ms 出现鱼鳔
      setTimeout(() => {
        setShowFish(true)
      }, 1500)
    }

    if (playerAction === 'Action_19' || playerAction === 'Action_20') {
      //成功收竿和 失败收杆时隐藏鱼鳔
      setShowFish(false)
    }
  }, [playerAction]);

  useFrame((state) => {
    if (areaData) {
      if (myPlayer.itemData && myPlayer.itemData.type === ItemType.FishingRod) {
        if (FishingAreaConfig.getInstance().checkInArea(areaData, myPlayer.position)) {
          setShowFishingButton(true)
        } else {
          setShowFishingButton(false)
        }
      } else {
        setShowFishingButton(false)
      }
    }
  })


  return (
    <group>
      {areaData && <group ref={floatRef}>
        <AudioSystemComponent _key={'scene_fish_float_' + areaId}/>
      </group>}
      {showFish && fishData && <SceneFishFloat fishData={fishData} initPos={fishInitPos.current}/>}
      {fishGlb && fishTargetPos &&
        <SceneFish gltf={fishGlb} initPos={fishInitPos.current} targetPos={fishTargetPos} facePos={fishFacePos}/>}
    </group>
  );
}

