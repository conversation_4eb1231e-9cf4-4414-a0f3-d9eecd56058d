import React, {useEffect, useRef, useState} from 'react'
import * as THREE from "three";
import {Quaternion} from "three";
import {GLTF} from "three-stdlib";
import {useFrame} from "@react-three/fiber";
import {getParticleSystem} from "@/world/Particles/ParticleSystem";
import createUseGame, {UseGameState} from "@/src/stores/useGame";
import {useAnimations} from "@react-three/drei";
import {ConfigManager} from "@/world/Config/ConfigManager";

class ParabolaTrajectory {
  private start: THREE.Vector3;
  private end: THREE.Vector3;
  private height: number;

  private a: number = 0;
  private xv: number = 0;
  private yv: number = 0;

  constructor(startPoint: THREE.Vector3, endPoint: THREE.Vector3, height: number) {
    this.start = startPoint;
    this.end = endPoint;
    this.height = height;

    // 计算抛物线参数
    this.calculateCoefficients();
  }

  calculateCoefficients() {
    // 解抛物线方程 y = ax² + bx + c
    // 经过起点 (x1,y1), 顶点 (xv, yv), 终点 (x2,y2)
    const x1 = this.start.x;
    const y1 = this.start.y;
    const x2 = this.end.x;
    const y2 = this.end.y;

    // 假设顶点在中间x位置，y为起点y + 高度
    const xv = (x1 + x2) / 2;
    const yv = Math.max(y1, y2) + this.height;

    // 使用顶点式 y = a(x - xv)² + yv
    // 代入起点求a
    this.a = (y1 - yv) / ((x1 - xv) * (x1 - xv));
    this.xv = xv;
    this.yv = yv;
  }

  getPosition(progress: number) {
    // progress 0到1之间的值
    const x = this.start.x + (this.end.x - this.start.x) * progress;
    const y = this.a * Math.pow((x - this.xv), 2) + this.yv;

    // 如果是3D空间，可以添加z坐标的线性插值
    const z = this.start.z + (this.end.z - this.start.z) * progress;

    return new THREE.Vector3(x, y, z);
  }
}


function FishAnimation({fishRoot, useGame, animations}: {
  animations: THREE.AnimationClip[];
  fishRoot: THREE.Object3D,
  useGame: any,
}) {

  /**
   * Character animations setup
   */
  const curAnimation = useGame((state: UseGameState) => state.curAnimation);
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);

  const {actions} = useAnimations(animations, fishRoot);

  const animationSet = useGame((state: UseGameState) => state.animationSet)

  useEffect(() => {
    if (animations.length === 0 || curAnimation === null || curAnimation === undefined) {
      return
    }
    const actionList: string[] = curAnimation.split('|')

    const actionKey = actionList[0]
    const actionSpeed = Number(actionList[1] || 1)

    const finishCall = () => {
      setCurAnimation('Action_fish_00')
    }
    // Play animation
    const action: THREE.AnimationAction | null = actions[actionKey || ''];
    if (action) {
      action.timeScale = actionSpeed
      // For jump and jump land animation, only play once and clamp when finish
      if (
        curAnimation !== 'Action_fish_00' &&
        curAnimation !== 'Action_fish_01'
      ) {
        action
        .reset()
        .fadeIn(0.2)
        .setLoop(THREE.LoopOnce, 0)
        .play();
        action.clampWhenFinished = true;
      } else {
        action.reset().fadeIn(0.2).play();
      }

      // When any action is clamp and finished reset animation
      (action as any)._mixer.addEventListener("finished", () => finishCall());

    }
    return () => {
      if (action) {
        // Fade out previous action
        action.fadeOut(0.2);

        // Clean up mixer listener, and empty the _listeners array
        (action as any)._mixer.removeEventListener("finished", () =>
          finishCall()
        );
        (action as any)._mixer._listeners = [];
      }
    };
  }, [actions, curAnimation, animationSet, animations]);

  return null
}


export default function SceneFish({gltf, initPos, targetPos, facePos}: {
  gltf: GLTF,
  initPos: THREE.Vector3
  targetPos: THREE.Vector3
  facePos: THREE.Vector3
}) {
  const particleSystem = getParticleSystem()
  const groupRef = useRef<THREE.Group>(null)
  const useGameRef = useRef(createUseGame())
  const setCurAnimation = useGameRef.current((state: UseGameState) => state.setCurAnimation);
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([]);
  const [trajectory, setTrajectories] = useState<ParabolaTrajectory | null>(null)
  const fishFlyTime = useRef(0)
  const fishFlySpeed = useRef(1)

  useEffect(() => {
    if (groupRef.current && targetPos) {
      groupRef.current.add(gltf.scene)
      groupRef.current.position.copy(initPos)
      setCurAnimation('Action_fish_00')
      setTimeout(() => {
        setCurAnimation('Action_fish_01')
      }, 800)
      setAnimations(gltf.animations)
      particleSystem.addParticle(initPos.clone(), new Quaternion(), './particles/Effect_water_splash.json', 2, 3000)
      initPos.y = targetPos.y
      setTrajectories(new ParabolaTrajectory(initPos, targetPos, 2))
      fishFlyTime.current = Date.now()
      ConfigManager.getInstance().getData((data) => {
        fishFlySpeed.current = data.fish_fly_speed
      })
    }
    return () => {
      setTrajectories(null)
    }
  }, [gltf, initPos, targetPos]);

  useFrame((state) => {
    if (groupRef.current && trajectory && fishFlyTime.current > 0) {  // 没有加载完成

      const progress = (Date.now() - fishFlyTime.current) / 1000 * fishFlySpeed.current
      const position = trajectory.getPosition(Math.min(progress, 1))
      const positionNext = trajectory.getPosition(Math.min(progress + 0.01, 1))
      groupRef.current.position.copy(position)
      if (position.y < positionNext.y) {
        groupRef.current.lookAt(positionNext)
      } else {
        facePos.y = position.y
        groupRef.current.lookAt(facePos)
      }
      if (progress >= 1) {
        setTrajectories(null)
      }
    }
  })

  return (
    <group ref={groupRef}>
      <FishAnimation animations={animations} fishRoot={gltf.scene} useGame={useGameRef.current}/>
    </group>
  );
}

