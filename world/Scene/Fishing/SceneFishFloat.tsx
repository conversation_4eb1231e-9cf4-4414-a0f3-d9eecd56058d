import React, {useEffect, useRef, useState} from 'react'
import * as THREE from "three";
import {Quaternion} from "three";
import {FishConfig, FishData, FishStatus} from "@/world/Config/FishConfig";
import {LoaderUtil} from "@/world/Util/LoaderUtil";
import createUseGame, {UseGameState} from "@/src/stores/useGame";
import {useAnimations} from "@react-three/drei";
import {useFrame} from "@react-three/fiber";
import {getParticleSystem} from "@/world/Particles/ParticleSystem";
import ParticleObject from "@/world/Particles/ParticleObject";


function FishFloatAnimation({fishRoot, useGame, animations}: {
  animations: THREE.AnimationClip[];
  fishRoot: THREE.Object3D,
  useGame: any,
}) {

  /**
   * Character animations setup
   */
  const curAnimation = useGame((state: UseGameState) => state.curAnimation);
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);

  const {actions} = useAnimations(animations, fishRoot);

  const animationSet = useGame((state: UseGameState) => state.animationSet)

  useEffect(() => {
    if (animations.length === 0 || curAnimation === null || curAnimation === undefined) {
      return
    }
    const actionList: string[] = curAnimation.split('|')

    const actionKey = actionList[0]
    const actionSpeed = Number(actionList[1] || 1)

    const finishCall = () => {
      setCurAnimation('Action_fish_02')
    }
    // Play animation
    const action: THREE.AnimationAction | null = actions[actionKey || ''];
    if (action) {
      action.timeScale = actionSpeed
      // For jump and jump land animation, only play once and clamp when finish
      if (
        curAnimation !== 'Action_fish_02' &&
        curAnimation !== 'Action_fish_03'
      ) {
        action
        .reset()
        .fadeIn(curAnimation === 'Action_fish_01' ? 0 : 0.2)
        .setLoop(THREE.LoopOnce, 0)
        .play();
        action.clampWhenFinished = true;
      } else {
        action.reset().fadeIn(0.2).play();
      }

      // When any action is clamp and finished reset animation
      (action as any)._mixer.addEventListener("finished", () => finishCall());

    }
    return () => {
      if (action) {
        // Fade out previous action
        action.fadeOut(0.2);

        // Clean up mixer listener, and empty the _listeners array
        (action as any)._mixer.removeEventListener("finished", () =>
          finishCall()
        );
        (action as any)._mixer._listeners = [];
      }
    };
  }, [actions, curAnimation, animationSet, animations]);

  return null
}

export default function SceneFishFloat({fishData, initPos}: {
  fishData: FishData,
  initPos: THREE.Vector3
}) {
  const particleSystem = getParticleSystem()
  const fishFloatObject = FishConfig.getInstance().getObject(fishData.id)
  const useGameRef = useRef(createUseGame())
  const useGame = useGameRef.current
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);
  const groupRef = useRef<THREE.Group>(null)

  const [fishObject, setFishObject] = useState<THREE.Object3D | null>(null)
  const [fishShadow, setFishShadow] = useState<THREE.Object3D | null>(null)
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([]);
  const [fishStatus, setFishStatus] = useState<FishStatus>(FishStatus.Hide)

  useEffect(() => {
    switch (fishStatus) {
      case FishStatus.Hide:
        if (fishShadow)
          fishShadow.visible = false
        return
      case FishStatus.Show:
        setCurAnimation('Action_fish_00')
        break
      case FishStatus.Idle:
        setCurAnimation('Action_fish_02')
        break
      case FishStatus.Bite:
        setCurAnimation('Action_fish_01|' + fishData.bite_speed)
        break
      case FishStatus.Catch:
        setCurAnimation('Action_fish_03')
        break
      default:
        setCurAnimation('Action_fish_02')
        break
    }
    if (fishShadow)
      fishShadow.visible = true
  }, [fishStatus]);

  useEffect(() => {
    if (groupRef.current) {
      const group = groupRef.current
      LoaderUtil.loadGlb('./assets/Prop/Action_fishing_01.glb', (gltf) => {
        group.add(gltf.scene)
        group.quaternion.copy(new THREE.Quaternion().setFromEuler(new THREE.Euler(0, 90 / 180 * Math.PI, 0)))
        group.position.copy(initPos)
        group.add(gltf.scene)
        setFishObject(gltf.scene)
        setAnimations(gltf.animations)
        const fish_shadow = gltf.scene.getObjectByName('fish_shadow')
        if (fish_shadow) {
          setFishShadow(fish_shadow)
          fish_shadow.visible = false
        }
        particleSystem.addParticle(initPos.clone(), new Quaternion(), './particles/Effect_water_splash.json', 1, 3000)
        // setCurAnimation('Action_fish_02')
      })
      return () => {
        group.clear()
      }
    }
  }, [fishData, initPos]);

  useFrame((state) => {
    setFishStatus(fishFloatObject.status)
  })

  return (
    <>
      <group ref={groupRef}>
        {
          fishStatus == FishStatus.Catch &&
          <ParticleObject url={"./particles/Effect_water_splash_01.json"} scale={1} time={-1}/>
        }
      </group>
      {
        fishObject &&
        <FishFloatAnimation fishRoot={fishObject} animations={animations} useGame={useGame}/>
      }
    </>
  );
}

