import React, {useEffect, useMemo, useRef, useState} from "react";
import {use<PERSON><PERSON>e, useThree} from "@react-three/fiber";
import * as THREE from "three";
import {Camera} from "three";
import AvatarData from "../../AvatarOrdinalsBrowser/renderAvatar/Avatar/Data/AvatarData";
import PlayerMesh from "./PlayerMesh";
import {ButlerUtil} from "@/world/Global/GlobalButlerUtil";
import {Raycaster} from "three/src/Three.Core";
import GlobalSpaceEvent, {CharacterType, GlobalDataKey, TransformData} from "@/world/Global/GlobalSpaceEvent";
import {IAppState, SCENE_TYPE} from "@/constant/type";
import GlobalSpace from "@/world/Global/GlobalSpace";
import PetModel from "@/world/Character/PetModel";
import CustomCameraController from "@/world/Util/FreeCameraController";
import {KeyboardControls} from "@react-three/drei";
import Ecctrl, {CustomEcctrlRigidBody} from "@/src/Ecctrl";
import {getCirclePoints} from "@/utils";
import {TreeData} from "@/world/Config/TreeConfig";
import {ConfigManager} from "@/world/Config/ConfigManager";
import {ItemData} from "@/world/Config/ItemConfig";
import createUseGame, {AnimationSet, UseGameState} from "@/src/stores/useGame";
import {HandItemDetail} from "@/world/HandItem/HandItem";
import AudioSystemComponent, {AudioSystem} from "@/world/Global/GlobalAudioSystem";
import {StoneData} from "@/world/Config/StoneConfig";
import {FishingAreaData} from "@/world/Config/FishingAreaConfig";
import {FishConfig, FishData, FishFloatObject, FishStatus} from "@/world/Config/FishConfig";
import {DecryptedDataJson} from "@/hooks/useFish";
import {GetGameNetWork} from "@/world/hooks/useNetWork";
import {POPOVER_HEIGHT} from "@/constant";
import SpeakUtil, {SpeakUtilData} from "@/world/Util/SpeakUtil";
import {MapConfig} from "@/world/Config/MapConfig";
import {KeyPressUtil} from "@/world/Global/GlobalKeyPressUtil";
import {AvatarActionConfig, AvatarActionData} from "@/world/Config/AvatarActionConfig";
import {GLTF} from "three-stdlib";


export enum AppGameApiKey {
  authTwitter = 'authTwitter',
  receiveAxe = 'receiveAxe',
  receiveTool = 'receiveTool',
  receivePickaxe = 'receivePickaxe',
  receiveFishingRod = 'receiveFishingRod',
  useFishingRod = 'useFishingRod',
  fishingSuccess = 'fishingSuccess',
  finishFishEgg = 'finishFishEgg',
  cutTree = 'cutTree',
  mining = 'mining',
  startDogEgg = 'startDogEgg',
  activityRule = 'activityRule',
  showCombo = 'showCombo',
  showRewards = 'showRewards',
  setLoaderType = 'setLoaderType',
  openSynthesis = 'openSynthesis',
  disconnectWallet = 'disconnectWallet',
  sendChat = 'sendChat',
  buyTools = 'buyTools',
  submitResources = 'submitResources',
  shareTweets = 'shareTweets',
  donateTokens = 'donateTokens',
  claimDrop = 'claimDrop',
  updateCommunityRank = 'updateCommunityRank',
  updateFishRank = 'updateFishRank',
  updateTreeRank = 'updateTreeRank',
  updateStoneRank = 'updateStoneRank',
  refreshRank = 'refreshRank',
  pickUpDrop = 'pickUpDrop',
  createParticle = 'createParticle',
}

function ShortcutControl() {
  const myPlayer = GetMyPlayer();
  const useGame = myPlayer.getUseGame();
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);
  const [actionMap, setActionMap] = useState<Map<string, AvatarActionData>>(new Map());

  useEffect(() => {
    const keyList = Array.from(actionMap.keys())
    const cancel = KeyPressUtil.registerKeyPress(keyList, false, (event) => {
      const action = actionMap.get(event.key)
      if (action) {
        setCurAnimation(action.name)
      }
    })
    return () => {
      cancel()
    }
  }, [actionMap])

  useEffect(() => {
    AvatarActionConfig.getInstance().getDataList((dataList: AvatarActionData[]) => {
      const map = new Map<string, AvatarActionData>()
      dataList.forEach((data: AvatarActionData) => {
        if (data.shortcut.length > 0) {
          map.set(data.shortcut, data)
        }
      })
      setActionMap(map)
    })
  }, [])


  return <></>
}

function NetSender({children, speakUtil, height}: {
  height: number
  speakUtil: SpeakUtilData
  children: React.ReactNode
}) {
  const myPlayer = GetMyPlayer();
  const useGame = myPlayer.getUseGame();
  const {scene} = useThree();
  const playerRef = useRef<THREE.Group>(null);
  const animationSet: AnimationSet = useGame((state: UseGameState) => state.animationSet)
  const curAnimation = useGame((state: UseGameState) => state.curAnimation);
  const gameNetWork = GetGameNetWork();
  const lastUpdateRef = useRef(new THREE.Vector3()); // 记录上次更新的位置

  useEffect(() => {
    myPlayer.setAppApi(AppGameApiKey.sendChat, (content: string) => {
      speakUtil.wordSpeak(content, () => {
      }, () => {
      })
    })
  }, [speakUtil]);

  useEffect(() => {
    if (playerRef.current) {
      gameNetWork.sendAnimation(curAnimation)
      if (curAnimation === animationSet.idle)
        gameNetWork.sendPosition(myPlayer.position, myPlayer.quaternion)
    }
  }, [curAnimation]);

  useFrame(() => {
    if (!playerRef.current) return;
    myPlayer.object = playerRef.current
    ButlerUtil.setPlayerObj(playerRef.current);
    playerRef.current.getWorldPosition(myPlayer.position);
    playerRef.current.getWorldQuaternion(myPlayer.quaternion);
    if (myPlayer.position.y < -8 || myPlayer.position.y > 10) {

      const curMapData = MapConfig.getInstance().getCurMapData()
      //地图没有完全加载出来的时候先不传送
      if (curMapData) {
        const transformPosition = new THREE.Vector3(myPlayer.position.x, 2, myPlayer.position.z);
        // 创建Raycaster实例
        const raycaster = new Raycaster();
        // 从中心点到生成的点发射射线
        const direction = new THREE.Vector3(0, -1, 0);
        raycaster.set(transformPosition, direction);
        raycaster.far = 8; //多检测1的长度 , 给出生点留出体积

        //解决特效object导致的报错
        const list: THREE.Object3D[] = []
        scene.traverse((value) => {
          if (value.type === "Mesh") {
            list.push(value)
          }
        })
        // 获取射线与场景中物体的相交情况
        const intersects = raycaster.intersectObjects(list);

        if (intersects.length === 0) {
          transformPosition.x = curMapData.offset[0] || 0
          transformPosition.z = curMapData.offset[2] || 0
        }
        GlobalSpaceEvent.SetDataValue<TransformData>(GlobalDataKey.TransformData, {
          position: transformPosition,
          characterType: CharacterType.Player,
          sceneType: SCENE_TYPE.None
        })
      }
    } else {
      // 输出实时位置
      // console.log("Current Position:", groupRef.current?.getWorldPosition(new THREE.Vector3()));
      if (lastUpdateRef.current.distanceTo(myPlayer.position) < 0.2) {
        return
      }
      myPlayer.hitTreeFinishCallback && myPlayer.hitTreeFinishCallback()
      myPlayer.hitStoneFinishCallback && myPlayer.hitStoneFinishCallback()
      lastUpdateRef.current.copy(myPlayer.position)
      GlobalSpace.updateCharacterPosition(myPlayer.position)

      gameNetWork.sendPosition(myPlayer.position, myPlayer.quaternion)
    }
  });
  return (
    <group position={[0, -0.92, 0]} ref={playerRef}>
      {/* Replace character model here */}
      <SpeakUtil spaceUtilData={speakUtil} height={height}/>
      {children}
    </group>
  )
}

class MyPlayerClass {
  position: THREE.Vector3
  quaternion: THREE.Quaternion
  targetDirection: THREE.Vector3 | null = null
  itemData: ItemData | null = null
  btcAddress: string = ''
  sessionId: string = ''
  UsePetInscriptionId: string = ''
  pickedList: string[] = []

  dogEasterEgg: IAppState['dogEasterEgg'] | null = null
  showEasterEggButton: boolean = false

  userBasicInfo: IAppState['userBasicInfo'] | null = null
  axeParams: IAppState['axeParams'] | null = null

  scene: THREE.Scene | null = null
  camera: Camera | null = null

  object: THREE.Object3D | null = null
  refreshTimeStamp = 0
  rigidBody: CustomEcctrlRigidBody | null = null

  hitTreeFinishCallback: (() => void) | null = null
  private hitTreeTimer: NodeJS.Timeout | null = null
  private lastTreeData: TreeData | null = null
  private hitTreeCallback: ((damage: number) => void) | null = null
  private hitTreeStartTime: number = 0
  private hitTreeLastTime: number = 0

  hitStoneFinishCallback: (() => void) | null = null
  private lastStoneData: StoneData | null = null
  private hitStoneCallback: ((damage: number) => void) | null = null
  private hitStoneStartTime: number = 0
  private hitStoneLastTime: number = 0
  private hitStoneTimer: NodeJS.Timeout | null = null


  private fishingTimers: NodeJS.Timeout[] = []
  private fishData: FishData | null = null
  private fishFloatObject: FishFloatObject | null = null
  private isFishingSuccessTime: boolean = false

  private appApiMap: Map<AppGameApiKey, any> = new Map()

  private serverItemIdMap: Map<string, boolean> = new Map()
  private destroyItemIdMap: Map<string, boolean> = new Map()

  private speakUtil: SpeakUtilData

  pizzaCount: number = -1

  constructor() {
    this.position = new THREE.Vector3(0, 0, 0)
    this.quaternion = new THREE.Quaternion()
    this.speakUtil = new SpeakUtilData()
  }

  getUseGame() {
    return GlobalSpace.getMyUseGame()
  }

  saveServerItemId(serverId: string) {
    this.serverItemIdMap.set(serverId, true)
  }

  isNewServerItemId(serverId: string) {
    return !this.serverItemIdMap.has(serverId)
  }

  destroyHandItem() {
    if (this.axeParams) {
      if (this.destroyItemIdMap.has(this.axeParams.userItemId || '')) {
        return
      }
      this.destroyItemIdMap.set(this.axeParams.userItemId || '', true)
    }
  }

  setAppApi(key: AppGameApiKey, func: any) {
    this.appApiMap.set(key, func)
  }

  callAppApi(key: AppGameApiKey, ...args: any[]) {
    const func = this.appApiMap.get(key)
    if (func) {
      func(...args)
    }
  }

  moveToPoint(point: THREE.Vector3, callback: () => void) {
    if (this.rigidBody && this.rigidBody.moveCharacterToPoint) {
      this.rigidBody.moveCharacterToPoint(point, () => {
        callback()
      })
    }
  }

  changeHandItem(itemData: ItemData | null) {
    this.itemData = itemData
  }

  startPizza() {
    this.pizzaCount = 0
    const gameNetWork = GetGameNetWork();
    gameNetWork.sendPizzaCount()
  }

  addPizza() {
    this.pizzaCount++
    AudioSystem.playAudio('myPlayer', './sound/reward/pick_up.wav', () => {
      return true
    })
    const gameNetWork = GetGameNetWork();
    gameNetWork.sendPizzaCount()
  }

  removePizza() {
    this.pizzaCount = -1
    const gameNetWork = GetGameNetWork();
    gameNetWork.sendPizzaCount()
  }

  facePosition(targetPosition: THREE.Vector3) {
    targetPosition.y = this.position.y
    this.targetDirection = this.position.clone().sub(targetPosition).normalize();
    if (this.rigidBody && this.rigidBody.rotateCharacterOnY) {
      const rotY = Math.atan2(-this.targetDirection.x, -this.targetDirection.z)
      this.rigidBody.rotateCharacterOnY(rotY, true)
    }
  }

  faceCamera() {
    if (this.camera) {
      this.facePosition(this.camera.position.clone())
    }
  }

  hitTree(callback: (isCombo: boolean, damage: number) => void, hitCallback: (damage: number, position: THREE.Vector3, quaternion: THREE.Quaternion) => void, finishCallback: () => void, failCallback: () => void, scene: THREE.Scene, treeData: TreeData) {
    if (this.lastTreeData && this.lastTreeData !== treeData) {
      //防止同时砍两棵树导致的异常
      return
    }

    const hitStart = () => {
      this.lastTreeData = treeData
      const debugItemWidth = Number(localStorage.getItem('debugItemWidth'))
      const itemWidth = debugItemWidth != 0 ? debugItemWidth : this.itemData?.axe_width || 0
      const debugItemLength = Number(localStorage.getItem('debugItemLength'))
      const itemLength = debugItemLength != 0 ? debugItemLength : this.itemData?.axe_length || 0
      ConfigManager.getInstance().getData((data) => {

        const facePoint = getCirclePoints(treeData.position[0], treeData.position[2], treeData.radius + itemWidth, this.position.x, this.position.z, itemLength)
        const treePos = new THREE.Vector3(treeData.position[0], treeData.position[1] + 0.5, treeData.position[2])
        const facePos = new THREE.Vector3(facePoint.x, treeData.position[1] + 0.5, facePoint.y)
        const hitPos = treePos.clone()
        hitPos.add(facePos.clone().sub(treePos).normalize().multiplyScalar(treeData.radius))
        // hitPos.y = treePos.y
        const success = (isCombo: boolean) => {
          this.hitTreeCallback = (damage: number) => {
            const object = new THREE.Object3D()
            object.position.copy(treePos)
            object.lookAt(facePos)
            hitCallback(damage, hitPos, object.quaternion)
          }
          this.hitTreeFinishCallback = () => {
            this.lastTreeData = null
            this.hitTreeStartTime = 0
            this.hitTreeLastTime = 0
            finishCallback()
            this.hitTreeFinishCallback = null
            this.hitTreeCallback = null
          }

          this.hitTreeLastTime = now
          const damage = this.itemData?.damage || 0
          callback(isCombo, damage)
        }

        const now = Date.now()
        if (this.hitTreeStartTime == 0) {
          this.hitTreeStartTime = now
          success(false)
        } else if (this.hitTreeStartTime > 0) {
          const hitTime = now - this.hitTreeLastTime
          const isFirst = this.hitTreeStartTime == this.hitTreeLastTime
          if (isFirst) {
            if (hitTime > data.tree_first_combo_range[0] && hitTime < data.tree_first_combo_range[1]) {
              success(true)
            } else {
              failCallback()
            }
          } else {
            if (hitTime > data.tree_second_combo_range[0] && hitTime < data.tree_second_combo_range[1]) {
              success(true)
            } else {
              failCallback()
            }
          }
        }

        const debugHitTree = localStorage.getItem('debugHitTree') === 'true'
        if (debugHitTree) {
          const geometry = new THREE.SphereGeometry(0.1);
          const material = new THREE.MeshBasicMaterial({color: 0xff0000});  // 红色高亮
          const sphere = new THREE.Mesh(geometry, material);
          sphere.position.copy(hitPos);
          scene.add(sphere);
          const material1 = new THREE.MeshBasicMaterial({color: 0x0000ff});  // 黄色高亮
          const sphere1 = new THREE.Mesh(geometry, material1);
          sphere1.position.copy(facePos);
          scene.add(sphere1);

          setTimeout(() => {
            scene.remove(sphere)
            scene.remove(sphere1)
          }, 2000)
        }
        this.facePosition(facePos)
      })
    }

    const direction = new THREE.Vector3(this.position.x - treeData.position[0], 0, this.position.z - treeData.position[2])
    const distance = direction.length()
    if (distance > treeData.range[1] && distance < treeData.range[2]) {
      hitStart()
    } else {
      const targetDistance = treeData.range[2] < distance ? treeData.range[1] : treeData.range[2]
      const targetPos = direction.normalize().multiplyScalar(targetDistance).add(new THREE.Vector3(treeData.position[0], treeData.position[1], treeData.position[2]))

      if (this.hitTreeTimer) {
        clearTimeout(this.hitTreeTimer);
        this.hitTreeTimer = null
      }
      this.moveToPoint(targetPos, () => {
        this.hitTreeTimer = setTimeout(() => {
          hitStart()
        }, 100)
      })
    }
  }


  hitStone(callback: (isCombo: boolean, damage: number) => void, hitCallback: (damage: number, position: THREE.Vector3, quaternion: THREE.Quaternion) => void, finishCallback: () => void, failCallback: () => void, scene: THREE.Scene, stoneData: StoneData) {
    if (this.lastStoneData && this.lastStoneData !== stoneData) {
      console.log('hit stone error')
      //防止同时砍两棵树导致的异常
      return
    }

    const hitStart = () => {
      this.lastStoneData = stoneData
      ConfigManager.getInstance().getData((data) => {

        const stonePos = new THREE.Vector3(stoneData.position[0], stoneData.position[1] + 0.5, stoneData.position[2])
        const facePos = stonePos.clone()
        const object = new THREE.Object3D()
        object.position.copy(this.position)
        object.lookAt(stonePos)
        const object2 = new THREE.Object3D()
        object2.position.set(0, 0.18, 1.5)
        object.add(object2)
        const hitPos = new THREE.Vector3(0, 0, 0)
        object2.getWorldPosition(hitPos)
        const success = (isCombo: boolean) => {
          this.hitStoneCallback = (damage: number) => {
            const object = new THREE.Object3D()
            object.position.copy(stonePos)
            object.lookAt(hitPos)
            hitCallback(damage, hitPos, object.quaternion)
          }
          this.hitStoneFinishCallback = () => {
            this.lastStoneData = null
            this.hitStoneStartTime = 0
            this.hitStoneLastTime = 0
            finishCallback()
            this.hitStoneFinishCallback = null
            this.hitStoneCallback = null
          }

          this.hitStoneLastTime = now
          const damage = this.itemData?.damage || 0
          callback(isCombo, damage)
        }

        const now = Date.now()
        if (this.hitStoneStartTime == 0) {
          this.hitStoneStartTime = now
          success(false)
        } else if (this.hitStoneStartTime > 0) {
          const hitTime = now - this.hitStoneLastTime
          const isFirst = this.hitStoneStartTime == this.hitStoneLastTime
          if (isFirst) {
            if (hitTime > data.stone_first_combo_range[0] && hitTime < data.stone_first_combo_range[1]) {
              success(true)
            } else {
              failCallback()
            }
          } else {
            if (hitTime > data.stone_second_combo_range[0] && hitTime < data.stone_second_combo_range[1]) {
              success(true)
            } else {
              failCallback()
            }
          }
        }
        const debugHitTree = localStorage.getItem('debugHitTree') === 'true'
        if (debugHitTree) {
          const geometry = new THREE.SphereGeometry(0.1);
          const material = new THREE.MeshBasicMaterial({color: 0xff0000});  // 红色高亮
          const sphere = new THREE.Mesh(geometry, material);
          sphere.position.copy(hitPos);
          scene.add(sphere);

          setTimeout(() => {
            scene.remove(sphere)
          }, 2000)
        }
        this.facePosition(facePos)
      })
    }

    const direction = new THREE.Vector3(this.position.x - stoneData.position[0], 0, this.position.z - stoneData.position[2])
    const distance = direction.length()
    if (distance > stoneData.range[1] && distance < stoneData.range[2]) {
      hitStart()
    } else {
      const targetDistance = stoneData.range[2] < distance ? stoneData.range[1] : stoneData.range[2]
      const targetPos = direction.normalize().multiplyScalar(targetDistance).add(new THREE.Vector3(stoneData.position[0], stoneData.position[1], stoneData.position[2]))

      if (this.hitStoneTimer) {
        clearTimeout(this.hitStoneTimer);
        this.hitStoneTimer = null
      }
      this.moveToPoint(targetPos, () => {
        this.hitStoneTimer = setTimeout(() => {
          hitStart()
        }, 100)
      })
    }
  }

  startFishing(fishCallback: (fishPos: THREE.Vector3) => void, callback: (fishData: FishData) => void, finishCallback: () => void, areaData: FishingAreaData) {
    if (this.fishingTimers.length > 0) {
      return
    }

    this.fishingTimers.push(setTimeout(() => {
      if (this.rigidBody && this.rigidBody.rotateCharacterOnY) {
        //-90 为了和npc的对齐
        this.rigidBody.rotateCharacterOnY(areaData.fishing_yaw / 180 * Math.PI, true)
        const object = new THREE.Object3D()
        object.position.copy(this.position)
        object.quaternion.setFromEuler(new THREE.Euler(0, areaData.fishing_yaw / 180 * Math.PI, 0))
        const fishObject = new THREE.Object3D()
        fishObject.position.set(-0.5, 0.5, 2.5)
        object.add(fishObject)
        const fishPos = fishObject.getWorldPosition(new THREE.Vector3())
        // 创建Raycaster实例
        const raycaster = new Raycaster();
        // 从中心点到生成的点发射射线
        const direction = new THREE.Vector3(0, -1, 0);
        raycaster.set(fishPos, direction);
        raycaster.far = 8; //多检测1的长度 , 给出生点留出体积
        const waterMesh = FishConfig.getInstance().getWaterMesh()
        if (waterMesh) {
          // 获取射线与场景中物体的相交情况
          const intersects = raycaster.intersectObjects([waterMesh]);
          if (intersects.length > 0) {
            const intersect = intersects[0]
            fishCallback(intersect.point)
          }
        } else {
          fishCallback(fishPos)
        }

        this.callAppApi(AppGameApiKey.useFishingRod, (json: DecryptedDataJson) => {
          if (json && json.tag) {

            const randomFishId = Number(json.tag)

            FishConfig.getInstance().getData(randomFishId, (fishData) => {
              this.fishData = fishData
              const fishFloatObject = FishConfig.getInstance().getObject(randomFishId)
              this.fishFloatObject = fishFloatObject
              callback(fishData)
              const timeObject = FishConfig.getInstance().randomFishTimeObject(fishData)
              fishFloatObject.status = FishStatus.Hide
              this.fishingTimers.push(setTimeout(() => {
                fishFloatObject.status = FishStatus.Show
              }, timeObject.showTime))
              this.fishingTimers.push(setTimeout(() => {
                fishFloatObject.status = FishStatus.Show
              }, timeObject.showTime + 3000))
              timeObject.biteTimeList.forEach((item) => {
                this.fishingTimers.push(setTimeout(() => {
                  fishFloatObject.status = FishStatus.Bite
                }, item.biteTime))
                this.fishingTimers.push(setTimeout(() => {
                  this.isFishingSuccessTime = true
                }, item.successStartTime))
                this.fishingTimers.push(setTimeout(() => {
                  this.isFishingSuccessTime = false
                }, item.successEndTime))
                this.fishingTimers.push(setTimeout(() => {
                  fishFloatObject.status = FishStatus.Idle
                }, item.biteEndTime))
              })

              this.fishingTimers.push(setTimeout(() => {
                this.fishingTimers.forEach((timer) => {
                  clearTimeout(timer);
                })
                this.fishingTimers = []
                finishCallback()
                this.fishData = null
              }, timeObject.totalTime))
            })
          } else {
            this.fishingTimers.forEach((timer) => {
              clearTimeout(timer);
            })
            this.fishingTimers = []
            finishCallback()
            this.fishData = null
          }
        })
      }
    }, 100))
  }

  stopFishing(callback: (success: boolean, fishData: FishData | null) => void) {
    this.fishingTimers.forEach((timer) => {
      clearTimeout(timer);
    })
    this.fishingTimers = []
    callback(this.isFishingSuccessTime, this.fishData)
    if (this.fishFloatObject) {
      this.fishFloatObject.status = this.isFishingSuccessTime ? FishStatus.Catch : FishStatus.Idle
      this.fishFloatObject = null
    }
    this.isFishingSuccessTime = false
    this.fishData = null
  }

  onUpdate() {
    const now = Date.now()
    if (this.hitTreeStartTime > 0) {
      ConfigManager.getInstance().getData((data) => {
        if (this.hitTreeFinishCallback) {
          const hitTime = now - this.hitTreeLastTime
          const firstHit = this.hitTreeLastTime == this.hitTreeStartTime
          if (firstHit) {
            if (hitTime > data.tree_first_combo_range[0]) {
              this.hitTreeCallback && this.hitTreeCallback(this.itemData?.damage || 0)
              this.hitTreeCallback = null
            }
            if (hitTime > data.tree_first_combo_range[2]) {
              this.hitTreeFinishCallback()
            }
          } else {
            if (hitTime > data.tree_second_combo_range[0]) {
              this.hitTreeCallback && this.hitTreeCallback(this.itemData?.damage || 0)
              this.hitTreeCallback = null
            }
            if (hitTime > data.tree_second_combo_range[2]) {
              this.hitTreeFinishCallback()
            }
          }
        }
      })
    }
    if (this.hitStoneStartTime > 0) {
      ConfigManager.getInstance().getData((data) => {
        if (this.hitStoneFinishCallback) {
          const hitTime = now - this.hitStoneLastTime
          const firstHit = this.hitStoneLastTime == this.hitStoneStartTime
          if (firstHit) {
            if (hitTime > data.stone_first_combo_range[0]) {
              this.hitStoneCallback && this.hitStoneCallback(this.itemData?.damage || 0)
              this.hitStoneCallback = null
            }
            if (hitTime > data.stone_first_combo_range[2]) {
              this.hitStoneFinishCallback()
            }
          } else {
            if (hitTime > data.stone_second_combo_range[0]) {
              this.hitStoneCallback && this.hitStoneCallback(this.itemData?.damage || 0)
              this.hitStoneCallback = null
            }
            if (hitTime > data.stone_second_combo_range[2]) {
              this.hitStoneFinishCallback()
            }
          }
        }
      })
    }
  }

  getElement() {
    const {scene, camera} = useThree()
    const initPos = new THREE.Vector3(this.position.x, this.position.y + 1, this.position.z)
    const useGame = this.getUseGame()
    const [myAvatarData, setMyAvatarData] = useState<AvatarData | null>(null)
    const [petId, setPetId] = useState<string>('');
    const [usePetInscriptionId, setUsePetInscriptionId] = useState<string>("");
    const [freeCamera, setFreeCamera] = useState<boolean>(false)
    const [stopControl, setStopControl] = useState<boolean>(false)
    const [sceneLoading, setSceneLoading] = useState<boolean>(true)
    const [height, setHeight] = useState<number>(0)
    const [keyboardMap, setKeyboardMap] = useState<{ name: string, keys: string[] }[]>([]);
    const activityData = useMemo(() => {
      return {
        pizzaCount: this.pizzaCount,
      }
    }, [])
    const itemDetail = useMemo(() => {
      return {
        isMe: true,
        itemId: Number(this.axeParams?.tag || 0),
        curDurability: Number(this.axeParams?.currentDurability || 0),
        showEffect: false,
        serverId: this.axeParams?.userItemId || "",
        useGame: createUseGame()
      } as HandItemDetail
    }, [])
    const playerRef = useRef<CustomEcctrlRigidBody>(null)
    useEffect(() => {
      KeyPressUtil.setCallback((enable) => {
        setStopControl(!enable)
      })
      const usePetInscriptionIdKey = GlobalSpaceEvent.ListenKeyDataChange<string>(GlobalDataKey.UsePetInscriptionId, (value) => {
        this.UsePetInscriptionId = value
        setUsePetInscriptionId(value)
      })
      const myAvatarDataKey = GlobalSpaceEvent.ListenKeyDataChange<AvatarData>(GlobalDataKey.MyAvatarData, (value) => {
        setMyAvatarData(null)
        setTimeout(() => {
          setMyAvatarData(value)
        }, 1)
      })

      const openFreeCameraKey = GlobalSpaceEvent.ListenKeyDataChange<boolean>(GlobalDataKey.OpenFreeCamera, (value) => {
        setFreeCamera(value)
      })

      const sceneLoadingKey = GlobalSpaceEvent.ListenKeyDataChange<boolean>(GlobalDataKey.SceneLoading, (value) => {
        setSceneLoading(value)
      })
      this.scene = scene
      this.camera = camera

      return () => {
        this.scene = null
        this.camera = null
        GlobalSpaceEvent.RemoveListener(GlobalDataKey.UsePetInscriptionId, usePetInscriptionIdKey)
        GlobalSpaceEvent.RemoveListener(GlobalDataKey.UsePetInscriptionId, myAvatarDataKey)
        GlobalSpaceEvent.RemoveListener(GlobalDataKey.OpenFreeCamera, openFreeCameraKey)
        GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneLoading, sceneLoadingKey)
      }
    }, []);

    useEffect(() => {
      if (stopControl || freeCamera) {
        setKeyboardMap([])
      } else {
        setKeyboardMap([
          {name: "forward", keys: ["ArrowUp", "KeyW"]},
          {name: "backward", keys: ["ArrowDown", "KeyS"]},
          {name: "leftward", keys: ["ArrowLeft", "KeyA"]},
          {name: "rightward", keys: ["ArrowRight", "KeyD"]},
          {name: "jump", keys: ["Space"]},
          {name: "walk", keys: ["Shift"]},
        ])
      }
    }, [stopControl, freeCamera]);

    useEffect(() => {
      if (myAvatarData && myAvatarData.petId) {
        setPetId(myAvatarData.petId)
      }
    }, [myAvatarData])

    useFrame(({camera}) => {
      this.rigidBody = playerRef.current
      this.onUpdate()

      if (sceneLoading) {
        setStopControl(true)
        return
      }
      setStopControl(!KeyPressUtil.getEnable())

      if (this.btcAddress) {
        const currentDurability = Number(this.axeParams?.currentDurability || 0)
        const itemId = Number(this.axeParams?.tag || 0)
        const serverId = this.axeParams?.userItemId || ''
        if (this.destroyItemIdMap.has(serverId)) {
          itemDetail.itemId = 0
          itemDetail.curDurability = 0
          itemDetail.serverId = ''
          itemDetail.showEffect = false
        } else {
          itemDetail.itemId = itemId
          itemDetail.serverId = serverId
          itemDetail.curDurability = currentDurability
          itemDetail.showEffect = this.dogEasterEgg !== null
        }
      } else {
        itemDetail.itemId = 0
        itemDetail.curDurability = 0
        itemDetail.serverId = ''
        itemDetail.showEffect = false
      }

      if (this.btcAddress) {
        activityData.pizzaCount = this.pizzaCount
      } else {
        activityData.pizzaCount = -1
      }
    })
    return (
      <>
        {
          keyboardMap.length > 0 && <PetModel petId={petId}/>
        }
        {
          freeCamera &&
          <CustomCameraController/>
        }
        <KeyboardControls map={keyboardMap}>
          <Ecctrl
            useGame={useGame}
            followLight
            mode="PointToMove"
            debug={false}
            disableFollowCam={freeCamera}
            animated={true}
            position={[initPos.x, initPos.y, initPos.z]}
            camInitDir={{x: 0.3, y: 0}}
            jumpVel={3.7}
            sprintJumpMult={1.05}
            camFollowMult={9}
            turnSpeed={11}
            sprintWalkMult={0.7}
            ref={playerRef}
          >
            <NetSender speakUtil={this.speakUtil} height={height}>
              {
                myAvatarData && !freeCamera &&
                <PlayerMesh
                  useGame={useGame}
                  avatarData={myAvatarData}
                  usePet={usePetInscriptionId}
                  itemDetail={itemDetail}
                  activityData={activityData}
                  onLoader={(gltf: GLTF) => {
                    let box = new THREE.Box3().setFromObject(gltf.scene);
                    // box 的 min 和 max 属性代表边界框的最小和最大坐标
                    setHeight(Math.max(box.max.y - box.min.y + POPOVER_HEIGHT, 0))
                  }}
                />
              }
              <ShortcutControl/>
            </NetSender>
            <AudioSystemComponent _key={'myPlayer'}/>
          </Ecctrl>
        </KeyboardControls>
      </>
    )
  }
}

let MyPlayer: MyPlayerClass | null = null

export function GetMyPlayer() {
  if (MyPlayer == null) {
    MyPlayer = new MyPlayerClass()
  }
  return MyPlayer
}