/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three'
import React, {useEffect, useRef, useState} from 'react'
import {OrbitControls} from '@react-three/drei'
import AvatarObject from "../../AvatarOrdinalsBrowser/renderAvatar/Avatar/AvatarObject";
import {useThree} from "@react-three/fiber";
import GlobalSpaceEvent, {CharacterType, GlobalDataKey, SpaceStatus} from "../Global/GlobalSpaceEvent";
import {GetMyPlayer} from "@/world/Character/MyPlayer";
import TransformTools from "@/world/Scene/TransformTools";
import {preloadAudio, preloadItemGlb} from "@/world/Util/LoaderUtil";

function SetCamera() {
    const {camera} = useThree();

    const controlsRef = useRef(null);
    useEffect(() => {
        // 设置位置
        camera.position.set(2, 1.5, 2);
        // 设置朝向 (看向场景原点)
        // camera.lookAt();
        if (controlsRef.current) {
            GlobalSpaceEvent.SetDataValue(GlobalDataKey.EditorControls, controlsRef.current)
        }
    }, [camera]);
    return (
        <OrbitControls ref={controlsRef}
                       target={[0, 1.0, 0]}
                       minDistance={1}
                       maxDistance={4}
                       maxPolarAngle={Math.PI / 20 * 11}
        />
    );
}
function PlayerModel() {
    const myPlayer = GetMyPlayer();
    preloadItemGlb()
    preloadAudio()
    return (
      <>
        {myPlayer.getElement()}
      </>
    )
}
export default function Model({avatarObj}: CharacterModelProps) {

    const groupRef = useRef<THREE.Group>(null);
    const [status, setStatus] = useState<SpaceStatus>(SpaceStatus.Avatar);
    const [object, setObject] = useState<THREE.Group | null>(null);
    const myPlayer = GetMyPlayer();
    const useGame = myPlayer.getUseGame()
    useEffect(() => {
        const spaceStatusKey = GlobalSpaceEvent.ListenKeyDataChange<SpaceStatus>(GlobalDataKey.SpaceStatus, (value) => {
            if (value === SpaceStatus.Avatar) {
                setObject(avatarObj.sceneGroup)
            } else {
                avatarObj.sceneGroup.removeFromParent()
                setObject(null)
            }
            setStatus(value)
        })

        return () => {
            GlobalSpaceEvent.RemoveListener(GlobalDataKey.SpaceStatus, spaceStatusKey)
        }
    }, []);

    useEffect(() => {
        if (groupRef.current && object) {
            groupRef.current.add(object);
        }
    }, [object, groupRef]);

    return (
        <>
            <TransformTools
              characterType={CharacterType.Player}
              useGame={useGame}
            />
            {
                (status === SpaceStatus.Game) &&
                <PlayerModel/>
            }
            {
                (status !== SpaceStatus.Game) && <group position={[0, 0, 0]}>
                    <SetCamera/>
                    {
                        (status === SpaceStatus.Avatar) && <group ref={groupRef}/>
                    }
                </group>
            }
        </>

    )
}

export type CharacterModelProps = {
    avatarObj: AvatarObject;
};

