import { ConfigManager, type GameData } from "./ConfigManager";

// GameData 筛选出menus和Tabs
type RankingConfigData = {
  menus: GameData["menus"];
};

export class RankingConfig {
  private static instance: RankingConfig;

  static getInstance() {
    if (!RankingConfig.instance) {
      RankingConfig.instance = new RankingConfig();
    }
    return RankingConfig.instance;
  }

  getData(cb: (data: RankingConfigData) => void) {
    return ConfigManager.getInstance().getData(cb);
  }
}
