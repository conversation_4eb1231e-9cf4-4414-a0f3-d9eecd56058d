import {ConfigManager} from "@/world/Config/ConfigManager";
import createUseGame from "@/src/stores/useGame";
import {IAppState} from "@/constant/type";

export type TreeData = {
  name: string;
  id: number;
  glb_url: string;
  position: number[];
  coin_position: number[];
  radius: number;
  range: number[];
  yawY: number;
  sector_range: number[];
  sector_range_start: number
  sector_range_length: number
  type: number;
  max_hp: number;
  tree_disappear_time: number;
  hit_effect_scale: number;
  hit_effect_during: number;
  fall_effect_scale: number;
  fall_effect_during: number;
  fall_effect_delay: number;

}

export type TreeObject = {
  tag: number;
  hp: number;
  useGame: any;
  combo: number;
  haveCoin: boolean;
  id: string;//for server
  status: string;//for server
  score: number;//for server
}

export class TreeConfig {
  private static instance: TreeConfig

  private treeDataMap: Map<number, { url: string, data: TreeData, object: TreeObject }>

  private eggTreeList: number[] = []
  private eggCutIndex: number = -1

  private constructor() {
    this.treeDataMap = new Map<number, { url: string, data: TreeData, object: TreeObject }>()
    ConfigManager.getInstance().downloadConfig('./config/tree/_totals.json', (data: TreeData[]) => {
      for (let i = 0; i < data.length; i++) {
        const treeData = data[i]
        treeData.radius = treeData.radius || 1
        treeData.range = treeData.range || [1, 2, 3, 4]
        treeData.yawY = treeData.yawY || 0
        treeData.type = treeData.type || 0
        treeData.position = treeData.position || [0, 0, 0]
        treeData.coin_position = treeData.coin_position || [1, 3, 0]
        treeData.tree_disappear_time = treeData.tree_disappear_time || 1000
        treeData.hit_effect_scale = treeData.hit_effect_scale || 0.2
        treeData.hit_effect_during = treeData.hit_effect_during || 5000
        treeData.fall_effect_scale = treeData.fall_effect_scale || 1
        treeData.fall_effect_during = treeData.fall_effect_during || 5000
        treeData.fall_effect_delay = treeData.fall_effect_delay || 500
        treeData.sector_range = treeData.sector_range || [0, 360]
        treeData.sector_range_start = treeData.sector_range[0] || 0
        treeData.sector_range_length = treeData.sector_range[1] - treeData.sector_range[0]
        if (treeData.sector_range_start > 180)
          treeData.sector_range_start = treeData.sector_range_start - 360
        if (treeData.sector_range_start < -180)
          treeData.sector_range_start = treeData.sector_range_start + 360
        this.treeDataMap.set(treeData.id, {
          url: '',
          data: treeData,
          object: {
            tag: 0,
            id: '',
            status: 'dead',
            combo: 0,
            hp: 100,
            score: 0,
            haveCoin: false,
            useGame: createUseGame()
          }
        })
      }
    })
  }

  static getInstance() {
    if (!TreeConfig.instance) {
      TreeConfig.instance = new TreeConfig()
    }
    return TreeConfig.instance
  }

  getData(id: number, cb: (data: TreeData) => void) {
    if (this.treeDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb)
      }, 500)
      return
    }

    const config = this.treeDataMap.get(id)
    if (config) {
      cb(config.data)
    } else {
      console.error('not found tree config id: ' + id)
    }
  }

  getObject(id: number) {
    const config = this.treeDataMap.get(id)
    if (config) {
      return config.object
    } else {
      console.error('not found tree config id: ' + id)
      return {
        hp: 0,
        useGame: createUseGame()
      } as TreeObject
    }
  }

  updateTreeData(address: string, easterEgg: IAppState['easterEgg'], treeList: IAppState['treeList']) {
    const updateEgg = () => {
      //确保 treeList更新完之后再进行

      if (address === '' || !easterEgg || easterEgg.status === 'finished' || !easterEgg.goOn) {
        this.eggTreeList = []
        this.eggCutIndex = -1
        return
      }
      const rule = easterEgg.rule || ''
      this.eggTreeList = rule.split(',').map(Number)
      if (this.eggTreeList.length > 0) {
        for (let i = 0; i < this.eggTreeList.length; i++) {
          const tag = this.eggTreeList[i]
          const object = this.getObject(tag)
          if (object) {
            if (object.status === 'dead') {
              this.eggCutIndex = i
            } else {
              return;
            }
          }
        }
      }
    }


    if (treeList) {
      let loadTreeCount = treeList.length
      for (let i = 0; i < treeList.length; i++) {
        const tree = treeList[i]
        const tag = Number(tree.tag)
        this.getData(tag, (data) => {
          loadTreeCount--
          const treeObject = this.getObject(Number(tree.tag))
          if (treeObject) {
            treeObject.tag = tag
            treeObject.status = tree.status
            treeObject.id = tree.id
            treeObject.score = tree.score
            if (treeObject.status === 'alive') {
              treeObject.hp = data.max_hp
            }
          }
          if (loadTreeCount === 0) {
            updateEgg()
            this.showCoinTree()
          }
        })
      }
    }
  }

  getHitSound(treeData: TreeData) {
    switch (treeData.max_hp) {
      case 140:
        const list1 = [
          './sound/chop/chop_tree_small_1.mp3',
          './sound/chop/chop_tree_small_2.mp3',
          './sound/chop/chop_tree_small_3.mp3',
        ]
        return list1[Math.floor(Math.random() * list1.length)]
      case 200:
        const list2 = [
          './sound/chop/chop_tree_medium_1.mp3',
          './sound/chop/chop_tree_medium_2.mp3',
          './sound/chop/chop_tree_medium_3.mp3',
        ]
        return list2[Math.floor(Math.random() * list2.length)]
      case 250:
        const list3 = [
          './sound/chop/chop_tree_large_1.mp3',
          './sound/chop/chop_tree_large_2.mp3',
          './sound/chop/chop_tree_large_3.mp3',
        ]
        return list3[Math.floor(Math.random() * list3.length)]
      default:
        return './sound/chop/chop_tree_small_1.mp3'
    }
  }

  getFallSound(treeData: TreeData) {
    switch (treeData.max_hp) {
      case 140:
        return './sound/fall/tree_trunk_small.mp3'
      case 200:
        return './sound/fall/tree_trunk_medium.mp3'
      case 250:
        return './sound/fall/tree_trunk_large.mp3'
      default:
        return './sound/fall/tree_trunk_small.mp3'
    }
  }

  cutTree(treeTag: number) {
    if (this.eggTreeList.length === 0) {
      return
    }
    const nextTreeTag = this.eggTreeList[this.eggCutIndex + 1]
    if (treeTag === nextTreeTag) {
      this.eggCutIndex++
      this.showCoinTree()
    } else {
      this.eggCutIndex = -1
      this.eggTreeList = []
    }
  }

  showCoinTree() {
    this.treeDataMap.forEach((data) => {
      data.object.haveCoin = false
    })
    const eggTreeTag = this.eggTreeList[this.eggCutIndex + 1]
    if (eggTreeTag) {
      const object = this.getObject(eggTreeTag)
      if (object.status === 'alive') {
        object.haveCoin = true
      } else {
        this.eggCutIndex = -1
        this.eggTreeList = []
      }
    }
  }

  getAliveTreeCount() {
    let count = 0
    this.treeDataMap.forEach((data) => {
      if (data.object.status === 'alive') {
        count++
      }
    })
    return count
  }
}