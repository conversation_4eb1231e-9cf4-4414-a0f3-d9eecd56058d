import {ConfigManager} from "@/world/Config/ConfigManager";
import {GetMyPlayer} from "@/world/Character/MyPlayer";


export type NpcChatData = {
  id: number;
  showRefreshTime: number;
  chatText: string;
  chatUrl: string;
  optionList: number[];
  chatAction: string;
  replayAction: number;
}

export class NpcChatConfig {
  private static instance: NpcChatConfig

  private npcChatDataMap: Map<number, { url: string, data: NpcChatData | undefined }>

  private constructor() {
    this.npcChatDataMap = new Map<number, { url: string, data: NpcChatData | undefined }>()
    ConfigManager.getInstance().downloadConfig('./config/npcChat/_ids.json', (data: { id: number, url: string }[]) => {
      for (let i = 0; i < data.length; i++) {
        const config = data[i]
        this.npcChatDataMap.set(config.id, {url: config.url, data: undefined})
      }
    })
  }

  static getInstance() {
    if (!NpcChatConfig.instance) {
      NpcChatConfig.instance = new NpcChatConfig()
    }
    return NpcChatConfig.instance
  }

  private loadMapData(config: { url: string, data: NpcChatData | undefined }, cb: (data: NpcChatData) => void) {
    ConfigManager.getInstance().downloadConfig(config.url, (data) => {
      const npcChatData = data as NpcChatData
      npcChatData.showRefreshTime = npcChatData.showRefreshTime || 0
      npcChatData.chatText = npcChatData.chatText || ''
      npcChatData.chatUrl = npcChatData.chatUrl || ''
      npcChatData.optionList = npcChatData.optionList || [0, 0, 0]
      npcChatData.chatAction = npcChatData.chatAction || 'Action_12'
      npcChatData.replayAction = npcChatData.replayAction || 0
      config.data = npcChatData
      cb(npcChatData)
    })
  }

  getData(id: number, cb: (data: NpcChatData) => void) {
    if (this.npcChatDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb)
      }, 500)
      return
    }

    const config = this.npcChatDataMap.get(id)
    if (config) {
      const data = config.data
      if (data) {
        cb(data)
      } else {
        this.loadMapData(config, cb)
      }
    } else {
      console.error('not found npc chat config id: ' + id)
    }
  }

  getWord(data: NpcChatData) {
    let content = data.chatText
    const myPlayer = GetMyPlayer()
    if (data.showRefreshTime === 1 && myPlayer.refreshTimeStamp > 0) {
      //时间戳转本地时间
      let date = new Date(myPlayer.refreshTimeStamp)
      content += '<br><br>**Tool reset time: ' + date.toLocaleString() + '**'
    }
    return content
  }
}