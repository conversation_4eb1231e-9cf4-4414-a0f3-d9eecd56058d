import * as THREE from 'three'
import {ConfigManager} from "@/world/Config/ConfigManager";

export type FishingAreaData = {
  name: string;
  id: number;
  show_area_box: number;
  debug_area_box: boolean;
  fishing_yaw: number;
  box_yaw: number;
  center_position: number[];
  length: number;
  width: number;
  height: number;
  object: THREE.Object3D;
}

export class FishingAreaConfig {
  private static instance: FishingAreaConfig

  private areaDataMap: Map<number, { url: string, data: FishingAreaData }>

  private constructor() {
    this.areaDataMap = new Map<number, { url: string, data: FishingAreaData }>()
    ConfigManager.getInstance().downloadConfig('./config/fishingArea/_totals.json', (data: FishingAreaData[]) => {
      for (let i = 0; i < data.length; i++) {
        const areaData = data[i]
        areaData.show_area_box = areaData.show_area_box || 0
        areaData.debug_area_box = areaData.show_area_box === 1
        areaData.fishing_yaw = areaData.fishing_yaw || 0
        areaData.box_yaw = areaData.box_yaw || 0
        areaData.center_position = areaData.center_position || [0, 0, 0]
        areaData.length = areaData.length || 0
        areaData.width = areaData.width || 0
        areaData.height = areaData.height || 0
        areaData.object = new THREE.Object3D()
        areaData.object.position.set(areaData.center_position[0], areaData.center_position[1], areaData.center_position[2])
        areaData.object.quaternion.setFromEuler(new THREE.Euler(0, areaData.box_yaw / 180 * Math.PI, 0))
        this.areaDataMap.set(areaData.id, {url: '', data: areaData})
      }
    })
  }

  static getInstance() {
    if (!FishingAreaConfig.instance) {
      FishingAreaConfig.instance = new FishingAreaConfig()
    }
    return FishingAreaConfig.instance
  }

  getData(id: number, cb: (data: FishingAreaData) => void) {
    if (this.areaDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb)
      }, 500)
      return
    }

    const config = this.areaDataMap.get(id)
    if (config) {
      cb(config.data)
    } else {
      console.error('not found area config id: ' + id)
    }
  }

  checkInArea(data: FishingAreaData, worldPoint: THREE.Vector3) {

    const object = data.object;

// 获取对象的世界矩阵的逆矩阵
    const inverseMatrix = new THREE.Matrix4();
    inverseMatrix.copy(object.matrixWorld).invert();

// 应用逆矩阵变换
    const localPoint = worldPoint.clone().applyMatrix4(inverseMatrix);

// 现在localPoint中就存储了相对坐标
    if (localPoint.x > -data.length / 2 && localPoint.x < data.length / 2 &&
      localPoint.y > -data.height / 2 && localPoint.y < data.height / 2 &&
      localPoint.z > -data.width / 2 && localPoint.z < data.width / 2) {
      return true
    }

    return false
  }

  showAreaBox(scene: THREE.Scene, data: FishingAreaData) {
    if (data.debug_area_box) {
      const box = new THREE.Box3(new THREE.Vector3(-data.length / 2, -data.height / 2, -data.width / 2), new THREE.Vector3(data.length / 2, data.height / 2, data.width / 2))
      const helper = new THREE.Box3Helper(box, new THREE.Color(0x0000ff))
      data.object.add(helper)
    }
    scene.add(data.object)
  }
}