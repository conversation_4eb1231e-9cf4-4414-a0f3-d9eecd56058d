2025-07-08 18:47:43,948 - INFO - 🚀 开始执行 JSON 文件上传任务 (代理模式)...
2025-07-08 18:47:43,963 - INFO - ✅ Vite 开发服务器正在运行
2025-07-08 18:47:43,963 - INFO - 🔌 正在通过代理服务器连接到 OBS...
2025-07-08 18:47:43,963 - INFO - 📍 代理服务器: http://192.168.30.140:3000/
2025-07-08 18:47:43,963 - INFO - 🎯 目标端点: obs.ap-southeast-1.myhuaweicloud.com
2025-07-08 18:47:43,964 - INFO - 🧪 测试代理连接...
2025-07-08 18:47:43,978 - INFO - ✅ 成功通过代理连接到 OBS
2025-07-08 18:47:43,978 - INFO - 找到 1 个 JSON 文件: ['./game_config.json']
2025-07-08 18:47:43,978 - INFO - 开始上传 1 个 JSON 文件...
2025-07-08 18:47:43,978 - INFO - 📡 使用代理服务器: http://192.168.30.140:3000/
2025-07-08 18:47:43,978 - INFO - [1/1] 正在处理: ./game_config.json
2025-07-08 18:47:43,978 - INFO - 准备上传文件: './game_config.json' 到 'website_public/json_files/game_config.json' (大小: 5679 字节)
2025-07-08 18:47:43,978 - INFO - 📡 通过代理服务器: http://192.168.30.140:3000/
2025-07-08 18:47:45,095 - INFO - ✅ 成功上传文件: 'website_public/json_files/game_config.json' (通过代理)
2025-07-08 18:47:45,096 - INFO - ✅ 成功上传: ./game_config.json
2025-07-08 18:47:45,096 - INFO - 上传完成! 成功: 1, 失败: 0
2025-07-08 18:47:45,097 - INFO - 上传报告已保存到: upload_report_proxy.txt
2025-07-08 18:47:45,098 - INFO - 🎉 所有文件上传成功!
2025-07-08 18:47:45,098 - INFO - OBS 客户端连接已关闭
